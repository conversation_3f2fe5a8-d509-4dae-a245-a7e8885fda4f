import { ReducersMapObject } from "redux";
import { handleActions, Action } from "redux-actions";
import { CommonFeatures } from "bwtk";
import { Actions } from "../Actions";

const { actionsToComputedPropertyName } = CommonFeatures;

const {
  closeLightbox,
  setlightboxData
} = actionsToComputedPropertyName(Actions);
export function getWidgetLightboxes(): ReducersMapObject<any, Action<any>> {
  return {
    lightboxData: handleActions<any | null>({
      [setlightboxData]: (state, { payload }: Action<any>) => payload,
      [closeLightbox]: () => null
    }, null)
  };
}
