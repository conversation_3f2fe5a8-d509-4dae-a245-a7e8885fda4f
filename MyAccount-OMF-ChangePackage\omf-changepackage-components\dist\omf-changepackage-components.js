/*! omf-changepackage-components (lib) 0.1.0 | bwtk 6.1.0 | 2025-08-22T18:33:31.039Z */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("bwtk"), require("redux-actions"), require("rxjs"), require("redux"), require("react"), require("react-intl"), require("redux-observable"), require("react-redux"));
	else if(typeof define === 'function' && define.amd)
		define("omf-changepackage-components", ["bwtk", "redux-actions", "rxjs", "redux", "react", "react-intl", "redux-observable", "react-redux"], factory);
	else if(typeof exports === 'object')
		exports["omf-changepackage-components"] = factory(require("bwtk"), require("redux-actions"), require("rxjs"), require("redux"), require("react"), require("react-intl"), require("redux-observable"), require("react-redux"));
	else
		root["omf-changepackage-components"] = factory(root["bwtk"], root["ReduxActions"], root["rxjs"], root["Redux"], root["React"], root["ReactIntl"], root["ReduxObservable"], root["ReactRedux"]);
})(self, (__WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_intl__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_react_redux__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./redux-persist/lib/stateReconciler/hardSet.js":
/*!******************************************************!*\
  !*** ./redux-persist/lib/stateReconciler/hardSet.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {



exports.__esModule = true;
exports["default"] = hardSet;

/*
  hardSet: 
    - hard set incoming state
*/
function hardSet(inboundState) {
  return inboundState;
}

/***/ }),

/***/ "./redux-persist/lib/storage/createWebStorage.js":
/*!*******************************************************!*\
  !*** ./redux-persist/lib/storage/createWebStorage.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {



exports.__esModule = true;
exports["default"] = createWebStorage;

var _getStorage = _interopRequireDefault(__webpack_require__(/*! ./getStorage */ "./redux-persist/lib/storage/getStorage.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function createWebStorage(type) {
  var storage = (0, _getStorage.default)(type);
  return {
    getItem: function getItem(key) {
      return new Promise(function (resolve, reject) {
        resolve(storage.getItem(key));
      });
    },
    setItem: function setItem(key, item) {
      return new Promise(function (resolve, reject) {
        resolve(storage.setItem(key, item));
      });
    },
    removeItem: function removeItem(key) {
      return new Promise(function (resolve, reject) {
        resolve(storage.removeItem(key));
      });
    }
  };
}

/***/ }),

/***/ "./redux-persist/lib/storage/getStorage.js":
/*!*************************************************!*\
  !*** ./redux-persist/lib/storage/getStorage.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {



exports.__esModule = true;
exports["default"] = getStorage;

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function noop() {}

var noopStorage = {
  getItem: noop,
  setItem: noop,
  removeItem: noop
};

function hasStorage(storageType) {
  if ((typeof self === "undefined" ? "undefined" : _typeof(self)) !== 'object' || !(storageType in self)) {
    return false;
  }

  try {
    var storage = self[storageType];
    var testKey = "redux-persist ".concat(storageType, " test");
    storage.setItem(testKey, 'test');
    storage.getItem(testKey);
    storage.removeItem(testKey);
  } catch (e) {
    if (true) console.warn("redux-persist ".concat(storageType, " test failed, persistence will be disabled."));
    return false;
  }

  return true;
}

function getStorage(type) {
  var storageType = "".concat(type, "Storage");
  if (hasStorage(storageType)) return self[storageType];else {
    if (true) {
      console.error("redux-persist failed to create sync storage. falling back to noop storage.");
    }

    return noopStorage;
  }
}

/***/ }),

/***/ "./redux-persist/lib/storage/session.js":
/*!**********************************************!*\
  !*** ./redux-persist/lib/storage/session.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {



exports.__esModule = true;
exports["default"] = void 0;

var _createWebStorage = _interopRequireDefault(__webpack_require__(/*! ./createWebStorage */ "./redux-persist/lib/storage/createWebStorage.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = (0, _createWebStorage.default)('session');

exports["default"] = _default;

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!************************************!*\
  !*** ../src/index.ts + 66 modules ***!
  \************************************/
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Actions: () => (/* reexport */ Actions),
  Assert: () => (/* reexport */ Assert),
  BaseClient: () => (/* reexport */ BaseClient),
  Components: () => (/* reexport */ Components),
  Context: () => (/* reexport */ Context),
  ContextProvider: () => (/* reexport */ ContextProvider),
  EFlowType: () => (/* reexport */ EFlowType),
  EModals: () => (/* reexport */ EModals),
  EReviewMode: () => (/* reexport */ EReviewMode),
  EWidgetName: () => (/* reexport */ EWidgetName),
  EWidgetRoute: () => (/* reexport */ EWidgetRoute),
  EWidgetStatus: () => (/* reexport */ EWidgetStatus),
  FilterRestrictionObservable: () => (/* reexport */ FilterRestrictionObservable),
  FormattedHTMLMessage: () => (/* reexport */ FormattedHTMLMessage),
  LifecycleEpics: () => (/* reexport */ LifecycleEpics),
  ModalEpics: () => (/* reexport */ ModalEpics),
  Models: () => (/* reexport */ Models),
  Omniture: () => (/* reexport */ Omniture),
  Reducers: () => (/* reexport */ Reducers),
  RestricitonsEpics: () => (/* reexport */ RestricitonsEpics),
  Utils: () => (/* reexport */ Utils),
  ValueOf: () => (/* reexport */ ValueOf),
  Volt: () => (/* reexport */ Volt),
  WidgetContext: () => (/* reexport */ WidgetContext),
  withContext: () => (/* reexport */ withContext)
});

// EXTERNAL MODULE: external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"}
var external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__("bwtk");
// EXTERNAL MODULE: external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"}
var external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__("redux-actions");
;// ../src/Actions/index.ts


var Actions;
(function (Actions) {
    Actions.setWidgetStatus = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SET_WIDGET_STATUS");
    Actions.setWidgetProps = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SET_WIDGET_PROPS");
    Actions.getData = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("GET_WIDGET_DATA");
    Actions.showHideLoader = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SHOW_HIDE_LOADER");
    Actions.errorOccured = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("ERROR_OCCURED");
    Actions.openLightbox = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("OPEN_LIGHTBOX");
    Actions.closeLightbox = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("CLOSE_LIGHTBOX");
    Actions.setlightboxData = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SET_LIGHTBOX_DATA");
    Actions.broadcastUpdate = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("PIPE_SEND_UPDATE", (function (action, delay) {
        if (delay === void 0) { delay = 0; }
        setTimeout(function () { return external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator.instance.getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.EventStream).send(action.type, action.payload); }, delay);
        return action;
    }));
    Actions.refreshTotals = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("REFRESH_TOTALS");
    Actions.toggleTVCategoriesTray = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("TOGGLE_TV_CATEGORIES_TRAY");
    Actions.setProductConfigurationTotal = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SET_PRODUCT_CONFIGURATION_TOTAL");
    Actions.continueFlow = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("FLOW_CONTINUE");
    Actions.handleNav = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("HANDLE_NAV");
    Actions.onContinue = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("HISTORY_ON_CONTINUE");
    Actions.historyGo = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("HISTORY_GO");
    Actions.historyBack = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("HISTORY_BACK");
    Actions.historyForward = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("HISTORY_FORWARD");
    Actions.applicationReset = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("APPLICATION_RESET");
    Actions.applicationExit = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("APPLICATION_EXIT");
    Actions.applicationLogout = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("APPLICATION_LOGOUT");
    Actions.setHistoryProvider = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("SET_HISTORY_PROVIDER");
    Actions.setAppointmentVisited = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("APPOINTMENT_PAGE_VISITED");
    Actions.widgetRenderComplete = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("WIDGET_RENDER_COMPLETE");
    Actions.raiseRestriction = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("RESTRICTION_OCCURRED");
    Actions.acceptRestriction = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("RESTRICTION_ACCEPTED");
    Actions.declineRestriction = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("RESTRICTION_DECLINED");
    Actions.finalizeRestriction = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("RESTRICTION_CYCLE_COMPLETE");
    Actions.clearCachedState = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("CLEAR_CACHED_STATE");
    Actions.omniPageLoaded = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("OMNITURE_PAGE_LOADED", (function (name, data) { return (name ? { name: name, data: data } : undefined); }));
    Actions.omniPageSubmit = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("OMNITURE_PAGE_SUBMIT");
    Actions.omniModalOpen = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)("OMNITURE_MODAL_OPEN");
})(Actions || (Actions = {}));

;// ./tslib/tslib.es6.mjs
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise, SuppressedError, Symbol, Iterator */

var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf ||
      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
  return extendStatics(d, b);
};

function __extends(d, b) {
  if (typeof b !== "function" && b !== null)
      throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
  extendStatics(d, b);
  function __() { this.constructor = d; }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

var __assign = function() {
  __assign = Object.assign || function __assign(t) {
      for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
      }
      return t;
  }
  return __assign.apply(this, arguments);
}

function __rest(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
      t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
              t[p[i]] = s[p[i]];
      }
  return t;
}

function __decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}

function __param(paramIndex, decorator) {
  return function (target, key) { decorator(target, key, paramIndex); }
}

function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
      var context = {};
      for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
      for (var p in contextIn.access) context.access[p] = contextIn.access[p];
      context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
      var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
      if (kind === "accessor") {
          if (result === void 0) continue;
          if (result === null || typeof result !== "object") throw new TypeError("Object expected");
          if (_ = accept(result.get)) descriptor.get = _;
          if (_ = accept(result.set)) descriptor.set = _;
          if (_ = accept(result.init)) initializers.unshift(_);
      }
      else if (_ = accept(result)) {
          if (kind === "field") initializers.unshift(_);
          else descriptor[key] = _;
      }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};

function __runInitializers(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};

function __propKey(x) {
  return typeof x === "symbol" ? x : "".concat(x);
};

function __setFunctionName(f, name, prefix) {
  if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
  return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};

function __metadata(metadataKey, metadataValue) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
}

function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
  return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
      function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
      step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}

function __generator(thisArg, body) {
  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
  function verb(n) { return function (v) { return step([n, v]); }; }
  function step(op) {
      if (f) throw new TypeError("Generator is already executing.");
      while (g && (g = 0, op[0] && (_ = 0)), _) try {
          if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          if (y = 0, t) op = [op[0] & 2, t.value];
          switch (op[0]) {
              case 0: case 1: t = op; break;
              case 4: _.label++; return { value: op[1], done: false };
              case 5: _.label++; y = op[1]; op = [0]; continue;
              case 7: op = _.ops.pop(); _.trys.pop(); continue;
              default:
                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                  if (t[2]) _.ops.pop();
                  _.trys.pop(); continue;
          }
          op = body.call(thisArg, _);
      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
  }
}

var __createBinding = Object.create ? (function(o, m, k, k2) {
  if (k2 === undefined) k2 = k;
  var desc = Object.getOwnPropertyDescriptor(m, k);
  if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
  }
  Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
  if (k2 === undefined) k2 = k;
  o[k2] = m[k];
});

function __exportStar(m, o) {
  for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);
}

function __values(o) {
  var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
      next: function () {
          if (o && i >= o.length) o = void 0;
          return { value: o && o[i++], done: !o };
      }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}

function __read(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  }
  catch (error) { e = { error: error }; }
  finally {
      try {
          if (r && !r.done && (m = i["return"])) m.call(i);
      }
      finally { if (e) throw e.error; }
  }
  return ar;
}

/** @deprecated */
function __spread() {
  for (var ar = [], i = 0; i < arguments.length; i++)
      ar = ar.concat(__read(arguments[i]));
  return ar;
}

/** @deprecated */
function __spreadArrays() {
  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
  for (var r = Array(s), k = 0, i = 0; i < il; i++)
      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
          r[k] = a[j];
  return r;
}

function __spreadArray(to, from, pack) {
  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
      if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i);
          ar[i] = from[i];
      }
  }
  return to.concat(ar || Array.prototype.slice.call(from));
}

function __await(v) {
  return this instanceof __await ? (this.v = v, this) : new __await(v);
}

function __asyncGenerator(thisArg, _arguments, generator) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var g = generator.apply(thisArg, _arguments || []), i, q = [];
  return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;
  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }
  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }
  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }
  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }
  function fulfill(value) { resume("next", value); }
  function reject(value) { resume("throw", value); }
  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }
}

function __asyncDelegator(o) {
  var i, p;
  return i = {}, verb("next"), verb("throw", function (e) { throw e; }), verb("return"), i[Symbol.iterator] = function () { return this; }, i;
  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }
}

function __asyncValues(o) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var m = o[Symbol.asyncIterator], i;
  return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
}

function __makeTemplateObject(cooked, raw) {
  if (Object.defineProperty) { Object.defineProperty(cooked, "raw", { value: raw }); } else { cooked.raw = raw; }
  return cooked;
};

var __setModuleDefault = Object.create ? (function(o, v) {
  Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
  o["default"] = v;
};

var ownKeys = function(o) {
  ownKeys = Object.getOwnPropertyNames || function (o) {
    var ar = [];
    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
    return ar;
  };
  return ownKeys(o);
};

function __importStar(mod) {
  if (mod && mod.__esModule) return mod;
  var result = {};
  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
  __setModuleDefault(result, mod);
  return result;
}

function __importDefault(mod) {
  return (mod && mod.__esModule) ? mod : { default: mod };
}

function __classPrivateFieldGet(receiver, state, kind, f) {
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}

function __classPrivateFieldSet(receiver, state, value, kind, f) {
  if (kind === "m") throw new TypeError("Private method is not writable");
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
}

function __classPrivateFieldIn(state, receiver) {
  if (receiver === null || (typeof receiver !== "object" && typeof receiver !== "function")) throw new TypeError("Cannot use 'in' operator on non-object");
  return typeof state === "function" ? receiver === state : state.has(receiver);
}

function __addDisposableResource(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose, inner;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
      if (async) inner = dispose;
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };
    env.stack.push({ value: value, dispose: dispose, async: async });
  }
  else if (async) {
    env.stack.push({ async: true });
  }
  return value;
}

var _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

function __disposeResources(env) {
  function fail(e) {
    env.error = env.hasError ? new _SuppressedError(e, env.error, "An error was suppressed during disposal.") : e;
    env.hasError = true;
  }
  var r, s = 0;
  function next() {
    while (r = env.stack.pop()) {
      try {
        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);
        if (r.dispose) {
          var result = r.dispose.call(r.value);
          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });
        }
        else s |= 1;
      }
      catch (e) {
        fail(e);
      }
    }
    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();
    if (env.hasError) throw env.error;
  }
  return next();
}

function __rewriteRelativeImportExtension(path, preserveJsx) {
  if (typeof path === "string" && /^\.\.?\//.test(path)) {
      return path.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {
          return tsx ? preserveJsx ? ".jsx" : ".js" : d && (!ext || !cm) ? m : (d + ext + "." + cm.toLowerCase() + "js");
      });
  }
  return path;
}

/* harmony default export */ const tslib_es6 = ({
  __extends,
  __assign,
  __rest,
  __decorate,
  __param,
  __esDecorate,
  __runInitializers,
  __propKey,
  __setFunctionName,
  __metadata,
  __awaiter,
  __generator,
  __createBinding,
  __exportStar,
  __values,
  __read,
  __spread,
  __spreadArrays,
  __spreadArray,
  __await,
  __asyncGenerator,
  __asyncDelegator,
  __asyncValues,
  __makeTemplateObject,
  __importStar,
  __importDefault,
  __classPrivateFieldGet,
  __classPrivateFieldSet,
  __classPrivateFieldIn,
  __addDisposableResource,
  __disposeResources,
  __rewriteRelativeImportExtension,
});

;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/isFunction.js
function isFunction(value) {
    return typeof value === 'function';
}
//# sourceMappingURL=isFunction.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/createErrorClass.js
function createErrorClass(createImpl) {
    var _super = function (instance) {
        Error.call(instance);
        instance.stack = new Error().stack;
    };
    var ctorFunc = createImpl(_super);
    ctorFunc.prototype = Object.create(Error.prototype);
    ctorFunc.prototype.constructor = ctorFunc;
    return ctorFunc;
}
//# sourceMappingURL=createErrorClass.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/UnsubscriptionError.js

var UnsubscriptionError = createErrorClass(function (_super) {
    return function UnsubscriptionErrorImpl(errors) {
        _super(this);
        this.message = errors
            ? errors.length + " errors occurred during unsubscription:\n" + errors.map(function (err, i) { return i + 1 + ") " + err.toString(); }).join('\n  ')
            : '';
        this.name = 'UnsubscriptionError';
        this.errors = errors;
    };
});
//# sourceMappingURL=UnsubscriptionError.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/arrRemove.js
function arrRemove(arr, item) {
    if (arr) {
        var index = arr.indexOf(item);
        0 <= index && arr.splice(index, 1);
    }
}
//# sourceMappingURL=arrRemove.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/Subscription.js




var Subscription = (function () {
    function Subscription(initialTeardown) {
        this.initialTeardown = initialTeardown;
        this.closed = false;
        this._parentage = null;
        this._finalizers = null;
    }
    Subscription.prototype.unsubscribe = function () {
        var e_1, _a, e_2, _b;
        var errors;
        if (!this.closed) {
            this.closed = true;
            var _parentage = this._parentage;
            if (_parentage) {
                this._parentage = null;
                if (Array.isArray(_parentage)) {
                    try {
                        for (var _parentage_1 = __values(_parentage), _parentage_1_1 = _parentage_1.next(); !_parentage_1_1.done; _parentage_1_1 = _parentage_1.next()) {
                            var parent_1 = _parentage_1_1.value;
                            parent_1.remove(this);
                        }
                    }
                    catch (e_1_1) { e_1 = { error: e_1_1 }; }
                    finally {
                        try {
                            if (_parentage_1_1 && !_parentage_1_1.done && (_a = _parentage_1.return)) _a.call(_parentage_1);
                        }
                        finally { if (e_1) throw e_1.error; }
                    }
                }
                else {
                    _parentage.remove(this);
                }
            }
            var initialFinalizer = this.initialTeardown;
            if (isFunction(initialFinalizer)) {
                try {
                    initialFinalizer();
                }
                catch (e) {
                    errors = e instanceof UnsubscriptionError ? e.errors : [e];
                }
            }
            var _finalizers = this._finalizers;
            if (_finalizers) {
                this._finalizers = null;
                try {
                    for (var _finalizers_1 = __values(_finalizers), _finalizers_1_1 = _finalizers_1.next(); !_finalizers_1_1.done; _finalizers_1_1 = _finalizers_1.next()) {
                        var finalizer = _finalizers_1_1.value;
                        try {
                            execFinalizer(finalizer);
                        }
                        catch (err) {
                            errors = errors !== null && errors !== void 0 ? errors : [];
                            if (err instanceof UnsubscriptionError) {
                                errors = __spreadArray(__spreadArray([], __read(errors)), __read(err.errors));
                            }
                            else {
                                errors.push(err);
                            }
                        }
                    }
                }
                catch (e_2_1) { e_2 = { error: e_2_1 }; }
                finally {
                    try {
                        if (_finalizers_1_1 && !_finalizers_1_1.done && (_b = _finalizers_1.return)) _b.call(_finalizers_1);
                    }
                    finally { if (e_2) throw e_2.error; }
                }
            }
            if (errors) {
                throw new UnsubscriptionError(errors);
            }
        }
    };
    Subscription.prototype.add = function (teardown) {
        var _a;
        if (teardown && teardown !== this) {
            if (this.closed) {
                execFinalizer(teardown);
            }
            else {
                if (teardown instanceof Subscription) {
                    if (teardown.closed || teardown._hasParent(this)) {
                        return;
                    }
                    teardown._addParent(this);
                }
                (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);
            }
        }
    };
    Subscription.prototype._hasParent = function (parent) {
        var _parentage = this._parentage;
        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));
    };
    Subscription.prototype._addParent = function (parent) {
        var _parentage = this._parentage;
        this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;
    };
    Subscription.prototype._removeParent = function (parent) {
        var _parentage = this._parentage;
        if (_parentage === parent) {
            this._parentage = null;
        }
        else if (Array.isArray(_parentage)) {
            arrRemove(_parentage, parent);
        }
    };
    Subscription.prototype.remove = function (teardown) {
        var _finalizers = this._finalizers;
        _finalizers && arrRemove(_finalizers, teardown);
        if (teardown instanceof Subscription) {
            teardown._removeParent(this);
        }
    };
    Subscription.EMPTY = (function () {
        var empty = new Subscription();
        empty.closed = true;
        return empty;
    })();
    return Subscription;
}());

var EMPTY_SUBSCRIPTION = Subscription.EMPTY;
function isSubscription(value) {
    return (value instanceof Subscription ||
        (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe)));
}
function execFinalizer(finalizer) {
    if (isFunction(finalizer)) {
        finalizer();
    }
    else {
        finalizer.unsubscribe();
    }
}
//# sourceMappingURL=Subscription.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/config.js
var config = {
    onUnhandledError: null,
    onStoppedNotification: null,
    Promise: undefined,
    useDeprecatedSynchronousErrorHandling: false,
    useDeprecatedNextContext: false,
};
//# sourceMappingURL=config.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/scheduler/timeoutProvider.js

var timeoutProvider = {
    setTimeout: function (handler, timeout) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        var delegate = timeoutProvider.delegate;
        if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {
            return delegate.setTimeout.apply(delegate, __spreadArray([handler, timeout], __read(args)));
        }
        return setTimeout.apply(void 0, __spreadArray([handler, timeout], __read(args)));
    },
    clearTimeout: function (handle) {
        var delegate = timeoutProvider.delegate;
        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);
    },
    delegate: undefined,
};
//# sourceMappingURL=timeoutProvider.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/reportUnhandledError.js


function reportUnhandledError(err) {
    timeoutProvider.setTimeout(function () {
        var onUnhandledError = config.onUnhandledError;
        if (onUnhandledError) {
            onUnhandledError(err);
        }
        else {
            throw err;
        }
    });
}
//# sourceMappingURL=reportUnhandledError.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/noop.js
function noop() { }
//# sourceMappingURL=noop.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/NotificationFactories.js
var COMPLETE_NOTIFICATION = (function () { return createNotification('C', undefined, undefined); })();
function errorNotification(error) {
    return createNotification('E', undefined, error);
}
function nextNotification(value) {
    return createNotification('N', value, undefined);
}
function createNotification(kind, value, error) {
    return {
        kind: kind,
        value: value,
        error: error,
    };
}
//# sourceMappingURL=NotificationFactories.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/errorContext.js

var context = null;
function errorContext(cb) {
    if (config.useDeprecatedSynchronousErrorHandling) {
        var isRoot = !context;
        if (isRoot) {
            context = { errorThrown: false, error: null };
        }
        cb();
        if (isRoot) {
            var _a = context, errorThrown = _a.errorThrown, error = _a.error;
            context = null;
            if (errorThrown) {
                throw error;
            }
        }
    }
    else {
        cb();
    }
}
function captureError(err) {
    if (config.useDeprecatedSynchronousErrorHandling && context) {
        context.errorThrown = true;
        context.error = err;
    }
}
//# sourceMappingURL=errorContext.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/Subscriber.js









var Subscriber = (function (_super) {
    __extends(Subscriber, _super);
    function Subscriber(destination) {
        var _this = _super.call(this) || this;
        _this.isStopped = false;
        if (destination) {
            _this.destination = destination;
            if (isSubscription(destination)) {
                destination.add(_this);
            }
        }
        else {
            _this.destination = EMPTY_OBSERVER;
        }
        return _this;
    }
    Subscriber.create = function (next, error, complete) {
        return new SafeSubscriber(next, error, complete);
    };
    Subscriber.prototype.next = function (value) {
        if (this.isStopped) {
            handleStoppedNotification(nextNotification(value), this);
        }
        else {
            this._next(value);
        }
    };
    Subscriber.prototype.error = function (err) {
        if (this.isStopped) {
            handleStoppedNotification(errorNotification(err), this);
        }
        else {
            this.isStopped = true;
            this._error(err);
        }
    };
    Subscriber.prototype.complete = function () {
        if (this.isStopped) {
            handleStoppedNotification(COMPLETE_NOTIFICATION, this);
        }
        else {
            this.isStopped = true;
            this._complete();
        }
    };
    Subscriber.prototype.unsubscribe = function () {
        if (!this.closed) {
            this.isStopped = true;
            _super.prototype.unsubscribe.call(this);
            this.destination = null;
        }
    };
    Subscriber.prototype._next = function (value) {
        this.destination.next(value);
    };
    Subscriber.prototype._error = function (err) {
        try {
            this.destination.error(err);
        }
        finally {
            this.unsubscribe();
        }
    };
    Subscriber.prototype._complete = function () {
        try {
            this.destination.complete();
        }
        finally {
            this.unsubscribe();
        }
    };
    return Subscriber;
}(Subscription));

var _bind = Function.prototype.bind;
function bind(fn, thisArg) {
    return _bind.call(fn, thisArg);
}
var ConsumerObserver = (function () {
    function ConsumerObserver(partialObserver) {
        this.partialObserver = partialObserver;
    }
    ConsumerObserver.prototype.next = function (value) {
        var partialObserver = this.partialObserver;
        if (partialObserver.next) {
            try {
                partialObserver.next(value);
            }
            catch (error) {
                handleUnhandledError(error);
            }
        }
    };
    ConsumerObserver.prototype.error = function (err) {
        var partialObserver = this.partialObserver;
        if (partialObserver.error) {
            try {
                partialObserver.error(err);
            }
            catch (error) {
                handleUnhandledError(error);
            }
        }
        else {
            handleUnhandledError(err);
        }
    };
    ConsumerObserver.prototype.complete = function () {
        var partialObserver = this.partialObserver;
        if (partialObserver.complete) {
            try {
                partialObserver.complete();
            }
            catch (error) {
                handleUnhandledError(error);
            }
        }
    };
    return ConsumerObserver;
}());
var SafeSubscriber = (function (_super) {
    __extends(SafeSubscriber, _super);
    function SafeSubscriber(observerOrNext, error, complete) {
        var _this = _super.call(this) || this;
        var partialObserver;
        if (isFunction(observerOrNext) || !observerOrNext) {
            partialObserver = {
                next: (observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined),
                error: error !== null && error !== void 0 ? error : undefined,
                complete: complete !== null && complete !== void 0 ? complete : undefined,
            };
        }
        else {
            var context_1;
            if (_this && config.useDeprecatedNextContext) {
                context_1 = Object.create(observerOrNext);
                context_1.unsubscribe = function () { return _this.unsubscribe(); };
                partialObserver = {
                    next: observerOrNext.next && bind(observerOrNext.next, context_1),
                    error: observerOrNext.error && bind(observerOrNext.error, context_1),
                    complete: observerOrNext.complete && bind(observerOrNext.complete, context_1),
                };
            }
            else {
                partialObserver = observerOrNext;
            }
        }
        _this.destination = new ConsumerObserver(partialObserver);
        return _this;
    }
    return SafeSubscriber;
}(Subscriber));

function handleUnhandledError(error) {
    if (config.useDeprecatedSynchronousErrorHandling) {
        captureError(error);
    }
    else {
        reportUnhandledError(error);
    }
}
function defaultErrorHandler(err) {
    throw err;
}
function handleStoppedNotification(notification, subscriber) {
    var onStoppedNotification = config.onStoppedNotification;
    onStoppedNotification && timeoutProvider.setTimeout(function () { return onStoppedNotification(notification, subscriber); });
}
var EMPTY_OBSERVER = {
    closed: true,
    next: noop,
    error: defaultErrorHandler,
    complete: noop,
};
//# sourceMappingURL=Subscriber.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/symbol/observable.js
var observable = (function () { return (typeof Symbol === 'function' && Symbol.observable) || '@@observable'; })();
//# sourceMappingURL=observable.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/identity.js
function identity(x) {
    return x;
}
//# sourceMappingURL=identity.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/util/pipe.js

function pipe() {
    var fns = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        fns[_i] = arguments[_i];
    }
    return pipeFromArray(fns);
}
function pipeFromArray(fns) {
    if (fns.length === 0) {
        return identity;
    }
    if (fns.length === 1) {
        return fns[0];
    }
    return function piped(input) {
        return fns.reduce(function (prev, fn) { return fn(prev); }, input);
    };
}
//# sourceMappingURL=pipe.js.map
;// ./bwtk/node_modules/rxjs/dist/esm5/internal/Observable.js







var Observable = (function () {
    function Observable(subscribe) {
        if (subscribe) {
            this._subscribe = subscribe;
        }
    }
    Observable.prototype.lift = function (operator) {
        var observable = new Observable();
        observable.source = this;
        observable.operator = operator;
        return observable;
    };
    Observable.prototype.subscribe = function (observerOrNext, error, complete) {
        var _this = this;
        var subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);
        errorContext(function () {
            var _a = _this, operator = _a.operator, source = _a.source;
            subscriber.add(operator
                ?
                    operator.call(subscriber, source)
                : source
                    ?
                        _this._subscribe(subscriber)
                    :
                        _this._trySubscribe(subscriber));
        });
        return subscriber;
    };
    Observable.prototype._trySubscribe = function (sink) {
        try {
            return this._subscribe(sink);
        }
        catch (err) {
            sink.error(err);
        }
    };
    Observable.prototype.forEach = function (next, promiseCtor) {
        var _this = this;
        promiseCtor = getPromiseCtor(promiseCtor);
        return new promiseCtor(function (resolve, reject) {
            var subscriber = new SafeSubscriber({
                next: function (value) {
                    try {
                        next(value);
                    }
                    catch (err) {
                        reject(err);
                        subscriber.unsubscribe();
                    }
                },
                error: reject,
                complete: resolve,
            });
            _this.subscribe(subscriber);
        });
    };
    Observable.prototype._subscribe = function (subscriber) {
        var _a;
        return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);
    };
    Observable.prototype[observable] = function () {
        return this;
    };
    Observable.prototype.pipe = function () {
        var operations = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            operations[_i] = arguments[_i];
        }
        return pipeFromArray(operations)(this);
    };
    Observable.prototype.toPromise = function (promiseCtor) {
        var _this = this;
        promiseCtor = getPromiseCtor(promiseCtor);
        return new promiseCtor(function (resolve, reject) {
            var value;
            _this.subscribe(function (x) { return (value = x); }, function (err) { return reject(err); }, function () { return resolve(value); });
        });
    };
    Observable.create = function (subscribe) {
        return new Observable(subscribe);
    };
    return Observable;
}());

function getPromiseCtor(promiseCtor) {
    var _a;
    return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;
}
function isObserver(value) {
    return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);
}
function isSubscriber(value) {
    return (value && value instanceof Subscriber) || (isObserver(value) && isSubscription(value));
}
//# sourceMappingURL=Observable.js.map
// EXTERNAL MODULE: external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"}
var external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__("rxjs");
;// ./redux-persist/es/constants.js
var KEY_PREFIX = 'persist:';
var FLUSH = 'persist/FLUSH';
var REHYDRATE = 'persist/REHYDRATE';
var PAUSE = 'persist/PAUSE';
var PERSIST = 'persist/PERSIST';
var PURGE = 'persist/PURGE';
var REGISTER = 'persist/REGISTER';
var DEFAULT_VERSION = -1;
;// ./redux-persist/es/stateReconciler/autoMergeLevel1.js
function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function autoMergeLevel1_ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { autoMergeLevel1_ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { autoMergeLevel1_ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

/*
  autoMergeLevel1: 
    - merges 1 level of substate
    - skips substate if already modified
*/
function autoMergeLevel1(inboundState, originalState, reducedState, _ref) {
  var debug = _ref.debug;

  var newState = _objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object


  if (inboundState && _typeof(inboundState) === 'object') {
    Object.keys(inboundState).forEach(function (key) {
      // ignore _persist data
      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration

      if (originalState[key] !== reducedState[key]) {
        if ( true && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);
        return;
      } // otherwise hard set the new value


      newState[key] = inboundState[key];
    });
  }

  if ( true && debug && inboundState && _typeof(inboundState) === 'object') console.log("redux-persist/stateReconciler: rehydrated keys '".concat(Object.keys(inboundState).join(', '), "'"));
  return newState;
}
;// ./redux-persist/es/createPersistoid.js

// @TODO remove once flow < 0.63 support is no longer required.
function createPersistoid(config) {
  // defaults
  var blacklist = config.blacklist || null;
  var whitelist = config.whitelist || null;
  var transforms = config.transforms || [];
  var throttle = config.throttle || 0;
  var storageKey = "".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);
  var storage = config.storage;
  var serialize;

  if (config.serialize === false) {
    serialize = function serialize(x) {
      return x;
    };
  } else if (typeof config.serialize === 'function') {
    serialize = config.serialize;
  } else {
    serialize = defaultSerialize;
  }

  var writeFailHandler = config.writeFailHandler || null; // initialize stateful values

  var lastState = {};
  var stagedState = {};
  var keysToProcess = [];
  var timeIterator = null;
  var writePromise = null;

  var update = function update(state) {
    // add any changed keys to the queue
    Object.keys(state).forEach(function (key) {
      if (!passWhitelistBlacklist(key)) return; // is keyspace ignored? noop

      if (lastState[key] === state[key]) return; // value unchanged? noop

      if (keysToProcess.indexOf(key) !== -1) return; // is key already queued? noop

      keysToProcess.push(key); // add key to queue
    }); //if any key is missing in the new state which was present in the lastState,
    //add it for processing too

    Object.keys(lastState).forEach(function (key) {
      if (state[key] === undefined && passWhitelistBlacklist(key) && keysToProcess.indexOf(key) === -1 && lastState[key] !== undefined) {
        keysToProcess.push(key);
      }
    }); // start the time iterator if not running (read: throttle)

    if (timeIterator === null) {
      timeIterator = setInterval(processNextKey, throttle);
    }

    lastState = state;
  };

  function processNextKey() {
    if (keysToProcess.length === 0) {
      if (timeIterator) clearInterval(timeIterator);
      timeIterator = null;
      return;
    }

    var key = keysToProcess.shift();
    var endState = transforms.reduce(function (subState, transformer) {
      return transformer.in(subState, key, lastState);
    }, lastState[key]);

    if (endState !== undefined) {
      try {
        stagedState[key] = serialize(endState);
      } catch (err) {
        console.error('redux-persist/createPersistoid: error serializing state', err);
      }
    } else {
      //if the endState is undefined, no need to persist the existing serialized content
      delete stagedState[key];
    }

    if (keysToProcess.length === 0) {
      writeStagedState();
    }
  }

  function writeStagedState() {
    // cleanup any removed keys just before write.
    Object.keys(stagedState).forEach(function (key) {
      if (lastState[key] === undefined) {
        delete stagedState[key];
      }
    });
    writePromise = storage.setItem(storageKey, serialize(stagedState)).catch(onWriteFail);
  }

  function passWhitelistBlacklist(key) {
    if (whitelist && whitelist.indexOf(key) === -1 && key !== '_persist') return false;
    if (blacklist && blacklist.indexOf(key) !== -1) return false;
    return true;
  }

  function onWriteFail(err) {
    // @TODO add fail handlers (typically storage full)
    if (writeFailHandler) writeFailHandler(err);

    if (err && "development" !== 'production') {
      console.error('Error storing data', err);
    }
  }

  var flush = function flush() {
    while (keysToProcess.length !== 0) {
      processNextKey();
    }

    return writePromise || Promise.resolve();
  }; // return `persistoid`


  return {
    update: update,
    flush: flush
  };
} // @NOTE in the future this may be exposed via config

function defaultSerialize(data) {
  return JSON.stringify(data);
}
;// ./redux-persist/es/getStoredState.js

function getStoredState_getStoredState(config) {
  var transforms = config.transforms || [];
  var storageKey = "".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);
  var storage = config.storage;
  var debug = config.debug;
  var deserialize;

  if (config.deserialize === false) {
    deserialize = function deserialize(x) {
      return x;
    };
  } else if (typeof config.deserialize === 'function') {
    deserialize = config.deserialize;
  } else {
    deserialize = defaultDeserialize;
  }

  return storage.getItem(storageKey).then(function (serialized) {
    if (!serialized) return undefined;else {
      try {
        var state = {};
        var rawState = deserialize(serialized);
        Object.keys(rawState).forEach(function (key) {
          state[key] = transforms.reduceRight(function (subState, transformer) {
            return transformer.out(subState, key, rawState);
          }, deserialize(rawState[key]));
        });
        return state;
      } catch (err) {
        if ( true && debug) console.log("redux-persist/getStoredState: Error restoring data ".concat(serialized), err);
        throw err;
      }
    }
  });
}

function defaultDeserialize(serial) {
  return JSON.parse(serial);
}
;// ./redux-persist/es/purgeStoredState.js

function purgeStoredState(config) {
  var storage = config.storage;
  var storageKey = "".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);
  return storage.removeItem(storageKey, warnIfRemoveError);
}

function warnIfRemoveError(err) {
  if (err && "development" !== 'production') {
    console.error('redux-persist/purgeStoredState: Error purging data stored state', err);
  }
}
;// ./redux-persist/es/persistReducer.js
function persistReducer_ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function persistReducer_objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { persistReducer_ownKeys(source, true).forEach(function (key) { persistReducer_defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { persistReducer_ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function persistReducer_defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }






var DEFAULT_TIMEOUT = 5000;
/*
  @TODO add validation / handling for:
  - persisting a reducer which has nested _persist
  - handling actions that fire before reydrate is called
*/

function persistReducer(config, baseReducer) {
  if (true) {
    if (!config) throw new Error('config is required for persistReducer');
    if (!config.key) throw new Error('key is required in persistor config');
    if (!config.storage) throw new Error("redux-persist: config.storage is required. Try using one of the provided storage engines `import storage from 'redux-persist/lib/storage'`");
  }

  var version = config.version !== undefined ? config.version : DEFAULT_VERSION;
  var debug = config.debug || false;
  var stateReconciler = config.stateReconciler === undefined ? autoMergeLevel1 : config.stateReconciler;
  var getStoredState = config.getStoredState || getStoredState_getStoredState;
  var timeout = config.timeout !== undefined ? config.timeout : DEFAULT_TIMEOUT;
  var _persistoid = null;
  var _purge = false;
  var _paused = true;

  var conditionalUpdate = function conditionalUpdate(state) {
    // update the persistoid only if we are rehydrated and not paused
    state._persist.rehydrated && _persistoid && !_paused && _persistoid.update(state);
    return state;
  };

  return function (state, action) {
    var _ref = state || {},
        _persist = _ref._persist,
        rest = _objectWithoutProperties(_ref, ["_persist"]); // $FlowIgnore need to update State type


    var restState = rest;

    if (action.type === PERSIST) {
      var _sealed = false;

      var _rehydrate = function _rehydrate(payload, err) {
        // dev warning if we are already sealed
        if ( true && _sealed) console.error("redux-persist: rehydrate for \"".concat(config.key, "\" called after timeout."), payload, err); // only rehydrate if we are not already sealed

        if (!_sealed) {
          action.rehydrate(config.key, payload, err);
          _sealed = true;
        }
      };

      timeout && setTimeout(function () {
        !_sealed && _rehydrate(undefined, new Error("redux-persist: persist timed out for persist key \"".concat(config.key, "\"")));
      }, timeout); // @NOTE PERSIST resumes if paused.

      _paused = false; // @NOTE only ever create persistoid once, ensure we call it at least once, even if _persist has already been set

      if (!_persistoid) _persistoid = createPersistoid(config); // @NOTE PERSIST can be called multiple times, noop after the first

      if (_persist) {
        // We still need to call the base reducer because there might be nested
        // uses of persistReducer which need to be aware of the PERSIST action
        return persistReducer_objectSpread({}, baseReducer(restState, action), {
          _persist: _persist
        });
      }

      if (typeof action.rehydrate !== 'function' || typeof action.register !== 'function') throw new Error('redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.');
      action.register(config.key);
      getStoredState(config).then(function (restoredState) {
        var migrate = config.migrate || function (s, v) {
          return Promise.resolve(s);
        };

        migrate(restoredState, version).then(function (migratedState) {
          _rehydrate(migratedState);
        }, function (migrateErr) {
          if ( true && migrateErr) console.error('redux-persist: migration error', migrateErr);

          _rehydrate(undefined, migrateErr);
        });
      }, function (err) {
        _rehydrate(undefined, err);
      });
      return persistReducer_objectSpread({}, baseReducer(restState, action), {
        _persist: {
          version: version,
          rehydrated: false
        }
      });
    } else if (action.type === PURGE) {
      _purge = true;
      action.result(purgeStoredState(config));
      return persistReducer_objectSpread({}, baseReducer(restState, action), {
        _persist: _persist
      });
    } else if (action.type === FLUSH) {
      action.result(_persistoid && _persistoid.flush());
      return persistReducer_objectSpread({}, baseReducer(restState, action), {
        _persist: _persist
      });
    } else if (action.type === PAUSE) {
      _paused = true;
    } else if (action.type === REHYDRATE) {
      // noop on restState if purging
      if (_purge) return persistReducer_objectSpread({}, restState, {
        _persist: persistReducer_objectSpread({}, _persist, {
          rehydrated: true
        }) // @NOTE if key does not match, will continue to default else below

      });

      if (action.key === config.key) {
        var reducedState = baseReducer(restState, action);
        var inboundState = action.payload; // only reconcile state if stateReconciler and inboundState are both defined

        var reconciledRest = stateReconciler !== false && inboundState !== undefined ? stateReconciler(inboundState, state, reducedState, config) : reducedState;

        var _newState = persistReducer_objectSpread({}, reconciledRest, {
          _persist: persistReducer_objectSpread({}, _persist, {
            rehydrated: true
          })
        });

        return conditionalUpdate(_newState);
      }
    } // if we have not already handled PERSIST, straight passthrough


    if (!_persist) return baseReducer(state, action); // run base reducer:
    // is state modified ? return original : return updated

    var newState = baseReducer(restState, action);
    if (newState === restState) return state;
    return conditionalUpdate(persistReducer_objectSpread({}, newState, {
      _persist: _persist
    }));
  };
}
// EXTERNAL MODULE: external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"}
var external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__("redux");
;// ./redux-persist/es/stateReconciler/autoMergeLevel2.js
function autoMergeLevel2_typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { autoMergeLevel2_typeof = function _typeof(obj) { return typeof obj; }; } else { autoMergeLevel2_typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return autoMergeLevel2_typeof(obj); }

function autoMergeLevel2_ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function autoMergeLevel2_objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { autoMergeLevel2_ownKeys(source, true).forEach(function (key) { autoMergeLevel2_defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { autoMergeLevel2_ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function autoMergeLevel2_defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

/*
  autoMergeLevel2: 
    - merges 2 level of substate
    - skips substate if already modified
    - this is essentially redux-perist v4 behavior
*/
function autoMergeLevel2(inboundState, originalState, reducedState, _ref) {
  var debug = _ref.debug;

  var newState = autoMergeLevel2_objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object


  if (inboundState && autoMergeLevel2_typeof(inboundState) === 'object') {
    Object.keys(inboundState).forEach(function (key) {
      // ignore _persist data
      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration

      if (originalState[key] !== reducedState[key]) {
        if ( true && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);
        return;
      }

      if (isPlainEnoughObject(reducedState[key])) {
        // if object is plain enough shallow merge the new values (hence "Level2")
        newState[key] = autoMergeLevel2_objectSpread({}, newState[key], {}, inboundState[key]);
        return;
      } // otherwise hard set


      newState[key] = inboundState[key];
    });
  }

  if ( true && debug && inboundState && autoMergeLevel2_typeof(inboundState) === 'object') console.log("redux-persist/stateReconciler: rehydrated keys '".concat(Object.keys(inboundState).join(', '), "'"));
  return newState;
}

function isPlainEnoughObject(o) {
  return o !== null && !Array.isArray(o) && autoMergeLevel2_typeof(o) === 'object';
}
;// ./redux-persist/es/persistCombineReducers.js



// combineReducers + persistReducer with stateReconciler defaulted to autoMergeLevel2
function persistCombineReducers(config, reducers) {
  config.stateReconciler = config.stateReconciler === undefined ? autoMergeLevel2 : config.stateReconciler;
  return persistReducer(config, (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(reducers));
}
;// ./redux-persist/es/persistStore.js
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance"); }

function _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }

function persistStore_ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function persistStore_objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { persistStore_ownKeys(source, true).forEach(function (key) { persistStore_defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { persistStore_ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function persistStore_defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }



var initialState = {
  registry: [],
  bootstrapped: false
};

var persistorReducer = function persistorReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;

  switch (action.type) {
    case REGISTER:
      return persistStore_objectSpread({}, state, {
        registry: [].concat(_toConsumableArray(state.registry), [action.key])
      });

    case REHYDRATE:
      var firstIndex = state.registry.indexOf(action.key);

      var registry = _toConsumableArray(state.registry);

      registry.splice(firstIndex, 1);
      return persistStore_objectSpread({}, state, {
        registry: registry,
        bootstrapped: registry.length === 0
      });

    default:
      return state;
  }
};

function persistStore(store, options, cb) {
  // help catch incorrect usage of passing PersistConfig in as PersistorOptions
  if (true) {
    var optionsToTest = options || {};
    var bannedKeys = ['blacklist', 'whitelist', 'transforms', 'storage', 'keyPrefix', 'migrate'];
    bannedKeys.forEach(function (k) {
      if (!!optionsToTest[k]) console.error("redux-persist: invalid option passed to persistStore: \"".concat(k, "\". You may be incorrectly passing persistConfig into persistStore, whereas it should be passed into persistReducer."));
    });
  }

  var boostrappedCb = cb || false;

  var _pStore = (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.createStore)(persistorReducer, initialState, options && options.enhancer ? options.enhancer : undefined);

  var register = function register(key) {
    _pStore.dispatch({
      type: REGISTER,
      key: key
    });
  };

  var rehydrate = function rehydrate(key, payload, err) {
    var rehydrateAction = {
      type: REHYDRATE,
      payload: payload,
      err: err,
      key: key // dispatch to `store` to rehydrate and `persistor` to track result

    };
    store.dispatch(rehydrateAction);

    _pStore.dispatch(rehydrateAction);

    if (boostrappedCb && persistor.getState().bootstrapped) {
      boostrappedCb();
      boostrappedCb = false;
    }
  };

  var persistor = persistStore_objectSpread({}, _pStore, {
    purge: function purge() {
      var results = [];
      store.dispatch({
        type: PURGE,
        result: function result(purgeResult) {
          results.push(purgeResult);
        }
      });
      return Promise.all(results);
    },
    flush: function flush() {
      var results = [];
      store.dispatch({
        type: FLUSH,
        result: function result(flushResult) {
          results.push(flushResult);
        }
      });
      return Promise.all(results);
    },
    pause: function pause() {
      store.dispatch({
        type: PAUSE
      });
    },
    persist: function persist() {
      store.dispatch({
        type: PERSIST,
        register: register,
        rehydrate: rehydrate
      });
    }
  });

  if (!(options && options.manualPersist)) {
    persistor.persist();
  }

  return persistor;
}
;// ./redux-persist/es/createMigrate.js

function createMigrate(migrations, config) {
  var _ref = config || {},
      debug = _ref.debug;

  return function (state, currentVersion) {
    if (!state) {
      if ( true && debug) console.log('redux-persist: no inbound state, skipping migration');
      return Promise.resolve(undefined);
    }

    var inboundVersion = state._persist && state._persist.version !== undefined ? state._persist.version : DEFAULT_VERSION;

    if (inboundVersion === currentVersion) {
      if ( true && debug) console.log('redux-persist: versions match, noop migration');
      return Promise.resolve(state);
    }

    if (inboundVersion > currentVersion) {
      if (true) console.error('redux-persist: downgrading version is not supported');
      return Promise.resolve(state);
    }

    var migrationKeys = Object.keys(migrations).map(function (ver) {
      return parseInt(ver);
    }).filter(function (key) {
      return currentVersion >= key && key > inboundVersion;
    }).sort(function (a, b) {
      return a - b;
    });
    if ( true && debug) console.log('redux-persist: migrationKeys', migrationKeys);

    try {
      var migratedState = migrationKeys.reduce(function (state, versionKey) {
        if ( true && debug) console.log('redux-persist: running migration for versionKey', versionKey);
        return migrations[versionKey](state);
      }, state);
      return Promise.resolve(migratedState);
    } catch (err) {
      return Promise.reject(err);
    }
  };
}
;// ./redux-persist/es/createTransform.js
function createTransform( // @NOTE inbound: transform state coming from redux on its way to being serialized and stored
inbound, // @NOTE outbound: transform state coming from storage, on its way to be rehydrated into redux
outbound) {
  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var whitelist = config.whitelist || null;
  var blacklist = config.blacklist || null;

  function whitelistBlacklistCheck(key) {
    if (whitelist && whitelist.indexOf(key) === -1) return true;
    if (blacklist && blacklist.indexOf(key) !== -1) return true;
    return false;
  }

  return {
    in: function _in(state, key, fullState) {
      return !whitelistBlacklistCheck(key) && inbound ? inbound(state, key, fullState) : state;
    },
    out: function out(state, key, fullState) {
      return !whitelistBlacklistCheck(key) && outbound ? outbound(state, key, fullState) : state;
    }
  };
}
;// ./redux-persist/es/index.js









// EXTERNAL MODULE: ./redux-persist/lib/stateReconciler/hardSet.js
var hardSet = __webpack_require__("./redux-persist/lib/stateReconciler/hardSet.js");
// EXTERNAL MODULE: ./redux-persist/lib/storage/session.js
var session = __webpack_require__("./redux-persist/lib/storage/session.js");
;// ../src/Utils/ExtractProp.ts
function ValueOf(from, path, propDefault) {
    if (!Boolean(path))
        return from || propDefault;
    var passEmptyValues = /^\?/.test(path || "");
    var props = (path || "").replace("?", "").split(".");
    var result = from;
    for (var i = 0; i < props.length; i++) {
        if (result
            && result[props[i]] !== undefined
            && result[props[i]] !== null
            && (passEmptyValues
                || (result[props[i]] !== 0
                    && result[props[i]] !== ""))) {
            result = result[props[i]];
        }
        else {
            result = propDefault;
        }
    }
    return result;
}

;// ../src/Utils/Assert.ts
var defaultOptions = {
    error: "ReferenceError: test is not defined",
    failEmptyString: true,
    failNullValue: true,
    failZeroValue: false,
    test: function () { return false; }
};
function Assert(target, options) {
    var _opts = {};
    Object.assign(_opts, defaultOptions, typeof options === "string" ? { error: options } : options);
    if (target === undefined ||
        _opts.failNullValue && target === null ||
        _opts.failZeroValue && target === 0 ||
        _opts.failEmptyString && target === "" ||
        _opts.test(target))
        throw _opts.error;
    return target;
}

;// ../src/Utils/FilterRestrictionObservable.ts






var _restrictions = [];
function filterRestriction(restriction, isFinal) {
    if (isFinal === void 0) { isFinal = true; }
    var response = [];
    if (Boolean(restriction)) {
        _restrictions = Array.isArray(restriction)
            ? restriction
            : [restriction];
    }
    if (Array.isArray(_restrictions) && _restrictions.length) {
        response.push(Actions.raiseRestriction(_restrictions.pop()));
    }
    if (isFinal) {
        response.push(Actions.setWidgetStatus(EWidgetStatus.RENDERED));
    }
    return response;
}
function FilterRestrictionObservable(response) {
    var observables = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        observables[_i - 1] = arguments[_i];
    }
    Assert(response, "Expected response object, but got undefined/null");
    Assert(observables, "Expected Array<ObservableInput>, but got undefined/null");
    sessionStorage.setItem("omf:Initilized", "yes");
    switch (true) {
        case !(response.data && typeof response.data === "object"):
            return [
                Actions.errorOccured(new Models.ErrorHandler(response.statusText, __assign(__assign({}, (typeof response.data === 'object' ? response.data : {})), { url: response.url }))),
                Actions.setWidgetStatus(EWidgetStatus.RENDERED)
            ];
        case !!ValueOf(response, "data.restriction", false):
            return filterRestriction(ValueOf(response, "data.restriction"));
        default:
            var restrictionActions = filterRestriction(ValueOf(response, "data.productOfferingDetail.restriction"), false);
            var genericOperations = [
                Actions.broadcastUpdate(Actions.setProductConfigurationTotal(ValueOf(response, "data.productOfferingDetail.productConfigurationTotal")))
            ];
            return external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat.apply(void 0, __spreadArray([external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of.apply(void 0, __spreadArray([], __read(restrictionActions), false)), external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of.apply(void 0, __spreadArray([], __read(genericOperations), false))], __read(observables), false));
    }
}

// EXTERNAL MODULE: external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"}
var external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__("react");
var external_root_React_commonjs2_react_commonjs_react_amd_react_default = /*#__PURE__*/__webpack_require__.n(external_root_React_commonjs2_react_commonjs_react_amd_react_);
// EXTERNAL MODULE: external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"}
var external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__("react-intl");
;// ../src/Utils/FormattedHTMLMessage.tsx



var FormattedHTMLMessage = function (_a) {
    var id = _a.id, _b = _a.values, values = _b === void 0 ? {} : _b, defaultMessage = _a.defaultMessage;
    var intl = (0,external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.useIntl)();
    try {
        var formatted_1 = intl.formatMessage({ id: id, defaultMessage: defaultMessage }, values);
        var currencyRegex = /\{(\w+),\s*number,\s*CAD\}/g;
        formatted_1 = formatted_1.replace(currencyRegex, function (_, key) {
            var value = values[key];
            if (value != null && !isNaN(value)) {
                return intl.formatNumber(value, {
                    style: 'currency',
                    currency: 'CAD',
                });
            }
            return '';
        });
        if (formatted_1.includes('{') && formatted_1.includes('}') && values) {
            Object.entries(values).forEach(function (_a) {
                var _b = __read(_a, 2), key = _b[0], value = _b[1];
                var placeholder = "{".concat(key, "}");
                formatted_1 = formatted_1.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), String(value));
            });
        }
        return external_root_React_commonjs2_react_commonjs_react_amd_react_default().createElement("span", { dangerouslySetInnerHTML: { __html: formatted_1 } });
    }
    catch (e) {
        console.warn("Error formatting HTML message: ".concat(id), e);
        var fallbackText = intl.messages[id] || defaultMessage || id;
        return external_root_React_commonjs2_react_commonjs_react_amd_react_default().createElement("span", { dangerouslySetInnerHTML: { __html: fallbackText } });
    }
};

;// ../src/Utils/index.ts






var Utils;
(function (Utils) {
    function getCurrentLocale(localization) {
        var locale = localization.locale.substr(0, 2);
        return __assign(__assign({}, localization), { formats: localization.formats[locale], messages: localization.messages[locale] });
    }
    Utils.getCurrentLocale = getCurrentLocale;
    function showLightbox(lightboxId) {
        var modal = $("#" + lightboxId);
        modal.modal("show");
    }
    Utils.showLightbox = showLightbox;
    function isLightboxOpen(lightboxId) {
        var modal = $("#" + lightboxId);
        return ValueOf(modal.data("bs.modal"), "_isShown", false);
    }
    Utils.isLightboxOpen = isLightboxOpen;
    function hideLightbox(lightboxId) {
        if (isLightboxOpen(lightboxId)) {
            var modal = $("#" + lightboxId);
            modal.modal("hide");
        }
    }
    Utils.hideLightbox = hideLightbox;
    function getCookie(cname) {
        var decodedCookie = decodeURIComponent(document.cookie);
        var name = cname + "=";
        var ca = decodedCookie.split(";");
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === " ") {
                c = c.substring(1);
            }
            if (c.indexOf(name) === 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }
    Utils.getCookie = getCookie;
    Utils.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    Utils.isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
    Utils.deviceType = function () {
        var windowWidth = $(window).outerWidth();
        var isMobile = windowWidth > 767 ? false : true;
        var isTablet = windowWidth > 991 ? false : true;
        return {
            isMobile: isMobile,
            isTablet: isTablet
        };
    };
    function debounce(func, wait, immediate) {
        var timeout;
        return function () {
            var context = this, args = arguments;
            var later = function () {
                timeout = null;
                if (!immediate)
                    func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow)
                func.apply(context, args);
        };
    }
    Utils.debounce = debounce;
    Utils.reducer = function (config, reducers) { return persistReducer(config, reducers); };
    Utils.persistStateExists = function (config) { return Boolean(localStorage.getItem("omf:".concat(config.key))); };
    Utils.clearCachedState = function (widgets) {
        widgets.forEach(function (widget) { return sessionStorage.removeItem("omf:".concat(widget)); });
    };
    Utils.persistConfig = function (name, blacklist) {
        if (blacklist === void 0) { blacklist = []; }
        return ({
            version: 1,
            keyPrefix: "omf:",
            storage: session["default"],
            stateReconciler: hardSet["default"],
            key: name,
            blacklist: __spreadArray(["localization", "widgetStatus", "error", "lightboxData", "restriction"], __read(blacklist), false)
        });
    };
    function getFlowType() {
        var flowType = sessionStorage.getItem("omf:Flowtype");
        if (!flowType) {
            var pathname = window.location.pathname;
            switch (true) {
                case pathname.indexOf("Changepackage/Internet") > 0:
                    flowType = EFlowType.INTERNET;
                    break;
                case pathname.indexOf("Changepackage/TV") > 0:
                    flowType = EFlowType.TV;
                    break;
                case pathname.indexOf("Add/TV") > 0:
                    flowType = EFlowType.ADDTV;
                    break;
                case pathname.indexOf("Bundle/") > 0:
                    flowType = EFlowType.BUNDLE;
                    break;
            }
        }
        return flowType;
    }
    Utils.getFlowType = getFlowType;
    function constructPageRoute(route, flowType) {
        flowType = flowType || getFlowType();
        switch (flowType) {
            case EFlowType.INTERNET:
                switch (route) {
                    case EWidgetRoute.INTERNET: return "/Changepackage/Internet";
                    case EWidgetRoute.APPOINTMENT: return "/Changepackage/Internet/Appointment";
                    case EWidgetRoute.REVIEW: return "/Changepackage/Internet/Review";
                    case EWidgetRoute.CONFIRMATION: return "/Changepackage/Internet/Confirmation";
                    default: return "/Changepackage";
                }
            case EFlowType.TV:
                switch (route) {
                    case EWidgetRoute.TV: return "/Changepackage/TV";
                    case EWidgetRoute.APPOINTMENT: return "/Changepackage/TV/Appointment";
                    case EWidgetRoute.REVIEW: return "/Changepackage/TV/Review";
                    case EWidgetRoute.CONFIRMATION: return "/Changepackage/TV/Confirmation";
                    default: return "/Changepackage";
                }
            case EFlowType.ADDTV:
                switch (route) {
                    case EWidgetRoute.TV: return "/Add/TV";
                    case EWidgetRoute.REVIEW: return "/Add/TV/Review";
                    case EWidgetRoute.CONFIRMATION: return "/Add/TV/Confirmation";
                    default: return "/Add";
                }
            case EFlowType.BUNDLE:
                switch (route) {
                    case EWidgetRoute.INTERNET: return "/Bundle/Internet";
                    case EWidgetRoute.TV: return "/Bundle/TV";
                    case EWidgetRoute.APPOINTMENT: return "/Bundle/Internet/Appointment";
                    case EWidgetRoute.REVIEW: return "/Bundle/Review";
                    case EWidgetRoute.CONFIRMATION: return "/Bundle/Confirmation";
                    default: return "/Bundle";
                }
        }
    }
    Utils.constructPageRoute = constructPageRoute;
    function getPageRoute() {
        var pathname = window.location.pathname;
        switch (true) {
            case pathname.indexOf("Review") > 0: return EWidgetRoute.REVIEW;
            case pathname.indexOf("Confirm") > 0: return EWidgetRoute.CONFIRMATION;
            case pathname.indexOf("Appoint") > 0: return EWidgetRoute.APPOINTMENT;
            case pathname.indexOf("Internet") > 0: return EWidgetRoute.INTERNET;
            case pathname.indexOf("TV") > 0: return EWidgetRoute.TV;
        }
        return undefined;
    }
    Utils.getPageRoute = getPageRoute;
    function getURLByFlowType(scheme) {
        var flowType = sessionStorage.getItem("omf:Flowtype") || "Internet";
        return scheme[flowType];
    }
    Utils.getURLByFlowType = getURLByFlowType;
    function appendRefreshOnce(path) {
        var isFistTime = !sessionStorage.getItem("omf:Initilized");
        return (path || "") + (isFistTime ? "?refreshCache=true" : "");
    }
    Utils.appendRefreshOnce = appendRefreshOnce;
})(Utils || (Utils = {}));





;// ../src/Models/VOLT.ts
var Volt;
(function (Volt) {
    var EOfferingState;
    (function (EOfferingState) {
        EOfferingState["Add"] = "add";
        EOfferingState["Delete"] = "delete";
        EOfferingState["Modify"] = "modify";
        EOfferingState["NoCharge"] = "noChange";
        EOfferingState["Remove"] = "Remove";
        EOfferingState["Change"] = "Change";
        EOfferingState["InitiallySelected"] = "InitiallySelected";
        EOfferingState["NewlySelected"] = "NewlySelected";
        EOfferingState["Removed"] = "removed";
        EOfferingState["NotSelected"] = "NotSelected";
        EOfferingState["Added"] = "Added";
        EOfferingState["UnSelected"] = "UnSelected";
        EOfferingState["Create"] = "Create";
        EOfferingState["NoChange"] = "NoChange";
    })(EOfferingState = Volt.EOfferingState || (Volt.EOfferingState = {}));
    var EOfferingType;
    (function (EOfferingType) {
        EOfferingType["BaseOffering"] = "BaseOffering";
        EOfferingType["GroupOffering"] = "GroupOffering";
        EOfferingType["SingleOffering"] = "SingleOffering";
    })(EOfferingType = Volt.EOfferingType || (Volt.EOfferingType = {}));
    var EDIsplayGroupKey;
    (function (EDIsplayGroupKey) {
        EDIsplayGroupKey["TV_BASE_PRODUCT"] = "TV_BASE_PRODUCT";
        EDIsplayGroupKey["ALACARTE"] = "ALACARTE";
        EDIsplayGroupKey["MOVIE"] = "MOVIE";
        EDIsplayGroupKey["TV"] = "TV";
        EDIsplayGroupKey["SPECIALITY_SPORTS"] = "SPECIALITY_SPORTS";
        EDIsplayGroupKey["ADD_ON"] = "ADD_ON";
        EDIsplayGroupKey["INTERNATIONAL"] = "INTERNATIONAL";
        EDIsplayGroupKey["INTERNATIONAL_COMBOS"] = "INTERNATIONAL_COMBOS";
        EDIsplayGroupKey["INTERNATIONAL_ALACARTE"] = "INTERNATIONAL_ALACARTE";
        EDIsplayGroupKey["BASE_PROGRAMMING"] = "BASE_PROGRAMMING";
        EDIsplayGroupKey["SPECIALITY_CHANNELS"] = "SPECIALITY_CHANNELS";
        EDIsplayGroupKey["OFFERS"] = "OFFERS";
        EDIsplayGroupKey["TV_BROWSE_ALL"] = "TV_BROWSE_ALL";
        EDIsplayGroupKey["PROMOTION"] = "PROMOTION";
        EDIsplayGroupKey["NONE"] = "NONE";
    })(EDIsplayGroupKey = Volt.EDIsplayGroupKey || (Volt.EDIsplayGroupKey = {}));
    var EProductOfferingType;
    (function (EProductOfferingType) {
        EProductOfferingType["PACKAGE"] = "BasePackage";
        EProductOfferingType["COMBO"] = "Combo";
        EProductOfferingType["CHANNEL"] = "Channel";
        EProductOfferingType["NONE"] = "None";
    })(EProductOfferingType = Volt.EProductOfferingType || (Volt.EProductOfferingType = {}));
    var EProductOfferingGroupType;
    (function (EProductOfferingGroupType) {
        EProductOfferingGroupType["Delta"] = "Delta";
        EProductOfferingGroupType["New"] = "New";
        EProductOfferingGroupType["Current"] = "Current";
        EProductOfferingGroupType["Default"] = "Default";
    })(EProductOfferingGroupType = Volt.EProductOfferingGroupType || (Volt.EProductOfferingGroupType = {}));
    var ELineOfBusiness;
    (function (ELineOfBusiness) {
        ELineOfBusiness["TV"] = "TV";
        ELineOfBusiness["Internet"] = "Internet";
    })(ELineOfBusiness = Volt.ELineOfBusiness || (Volt.ELineOfBusiness = {}));
    var EAppointmentDuration;
    (function (EAppointmentDuration) {
        EAppointmentDuration["AM"] = "AM";
        EAppointmentDuration["PM"] = "PM";
        EAppointmentDuration["Evening"] = "Evening";
        EAppointmentDuration["AllDay"] = "AllDay";
        EAppointmentDuration["Item0810"] = "Item0810";
        EAppointmentDuration["Item1012"] = "Item1012";
        EAppointmentDuration["Item1315"] = "Item1315";
        EAppointmentDuration["Item1517"] = "Item1517";
        EAppointmentDuration["Item1719"] = "Item1719";
        EAppointmentDuration["Item1921"] = "Item1921";
    })(EAppointmentDuration = Volt.EAppointmentDuration || (Volt.EAppointmentDuration = {}));
    var EPreferredContactMethod;
    (function (EPreferredContactMethod) {
        EPreferredContactMethod["EMAIL"] = "Email";
        EPreferredContactMethod["TEXT_MESSAGE"] = "TextMessage";
        EPreferredContactMethod["PHONE"] = "Phone";
    })(EPreferredContactMethod = Volt.EPreferredContactMethod || (Volt.EPreferredContactMethod = {}));
})(Volt || (Volt = {}));

;// ../src/Models/index.ts





var EModals;
(function (EModals) {
    EModals["PREVIEWMODAL"] = "PREVIEW_MODAL";
})(EModals || (EModals = {}));
var EWidgetStatus;
(function (EWidgetStatus) {
    EWidgetStatus[EWidgetStatus["INIT"] = 0] = "INIT";
    EWidgetStatus[EWidgetStatus["RENDERED"] = 1] = "RENDERED";
    EWidgetStatus[EWidgetStatus["UPDATING"] = 2] = "UPDATING";
    EWidgetStatus[EWidgetStatus["ERROR"] = 3] = "ERROR";
    EWidgetStatus[EWidgetStatus["OUTAGERROR"] = 4] = "OUTAGERROR";
})(EWidgetStatus || (EWidgetStatus = {}));
var EWidgetRoute;
(function (EWidgetRoute) {
    EWidgetRoute["INTERNET"] = "/Internet";
    EWidgetRoute["TV"] = "/TV";
    EWidgetRoute["TV_Packages"] = "/Packages";
    EWidgetRoute["TV_MoviesSeries"] = "/Movies";
    EWidgetRoute["TV_Addons"] = "/Addons";
    EWidgetRoute["TV_Alacarte"] = "/Alacarte";
    EWidgetRoute["TV_International"] = "/International";
    EWidgetRoute["TV_InternationalCombos"] = "/International/Combos";
    EWidgetRoute["TV_InternationalAlacarte"] = "/International/Alacarte";
    EWidgetRoute["TV_Browse"] = "/Browse";
    EWidgetRoute["TV_Search"] = "/Search";
    EWidgetRoute["APPOINTMENT"] = "/Appointment";
    EWidgetRoute["REVIEW"] = "/Review";
    EWidgetRoute["CONFIRMATION"] = "/Confirmation";
})(EWidgetRoute || (EWidgetRoute = {}));
var EReviewMode;
(function (EReviewMode) {
    EReviewMode["Summary"] = "summary";
    EReviewMode["Review"] = "review";
    EReviewMode["Confirmation"] = "confirmation";
})(EReviewMode || (EReviewMode = {}));
var EWidgetName;
(function (EWidgetName) {
    EWidgetName["NAVIGATION"] = "omf-changepackage-navigation";
    EWidgetName["INTERNET"] = "omf-changepackage-internet";
    EWidgetName["TV"] = "omf-changepackage-tv";
    EWidgetName["APPOINTMENT"] = "omf-changepackage-appointment";
    EWidgetName["PREVIEW"] = "omf-changepackage-review";
    EWidgetName["REVIEW"] = "omf-changepackage-review";
    EWidgetName["CONFIRMATION"] = "omf-changepackage-review";
})(EWidgetName || (EWidgetName = {}));
var EFlowType;
(function (EFlowType) {
    EFlowType["INTERNET"] = "Internet";
    EFlowType["TV"] = "TV";
    EFlowType["ADDTV"] = "AddTV";
    EFlowType["BUNDLE"] = "Bundle";
})(EFlowType || (EFlowType = {}));
var Models;
(function (Models) {
    var EBrand;
    (function (EBrand) {
        EBrand["BELL"] = "B";
        EBrand["VIRGIN"] = "V";
        EBrand["LUCKY"] = "L";
    })(EBrand = Models.EBrand || (Models.EBrand = {}));
    var ErrorHandler = (function (_super) {
        __extends(ErrorHandler, _super);
        function ErrorHandler(message, details, debug) {
            if (debug === void 0) { debug = false; }
            var _this = _super.call(this, typeof message === "object" ? message.message : message) || this;
            _this.debug = false;
            _this.debug = debug || Boolean(Utils.getCookie("debugwidget"));
            if (typeof message === "object") {
                _this.stack = message.stack;
            }
            if (typeof details === "object") {
                switch (true) {
                    case Boolean(details["url"]):
                        _this.type = "API";
                        _this.response = details;
                        break;
                    case Boolean(details["componentStack"]):
                        _this.type = "widget";
                        _this.componentStack = details.componentStack;
                        break;
                }
            }
            else {
                _this.type = "logic";
            }
            return _this;
        }
        return ErrorHandler;
    }(Error));
    Models.ErrorHandler = ErrorHandler;
    function ErrorHandlerObservable(action) {
        return function (response, source) {
            var err = response.error || response;
            return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.merge)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(Actions.errorOccured(new ErrorHandler(action.toString(), err))), source);
        };
    }
    Models.ErrorHandlerObservable = ErrorHandlerObservable;
    Models.noSpecialCharRegex = RegExp(/^[a-zA-Z0-9]+$/i);
    Models.emailRegex = RegExp(/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i);
    Models.phoneRegex = RegExp(/^[0-9]\d{2}-\d{3}-\d{4}$/i);
    Models.hashCommaRegex = RegExp(/[\,\#]/i);
    Models.onlyNumbersRegex = RegExp(/^[0-9]+$/i);
})(Models || (Models = {}));

;// ../src/Client/index.ts





var _isDebug = Boolean(Utils.getCookie("debugwidget"));
function createObservableRequest(method) {
    return Observable.create(function (observer) {
        method()
            .then(function (response) {
            observer.next(response);
        })
            .catch(function (error) {
            observer.error(error);
        })
            .then(function () {
            observer.complete();
        });
    });
}
function mockResponder(data) {
    return createObservableRequest(function () { return new Promise(function (resolve) {
        setTimeout(function () { return resolve({ data: data }); }, 350);
    }); });
}
function cleanPath(path) {
    return (path || "").split("/").pop();
}
var BaseClient = (function (_super) {
    __extends(BaseClient, _super);
    function BaseClient(ajax, config) {
        var _this = _super.call(this, ajax) || this;
        _this.config = config;
        return _this;
    }
    Object.defineProperty(BaseClient.prototype, "useMockData", {
        get: function () {
            return (_isDebug && this.config.mockdata !== undefined) ||
                (this.config.mockdata !== undefined && this.config.environmentVariables.useMockData);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseClient.prototype, "mockdata", {
        get: function () {
            return this.config.mockdata || {};
        },
        enumerable: false,
        configurable: true
    });
    BaseClient.prototype.get = function (path, _data, _options) {
        var mock = ValueOf(this.mockdata, cleanPath(path) + ".GET", false);
        return this.useMockData &&
            mock ?
            mockResponder(mock)
            : _super.prototype.get.apply(this, arguments);
    };
    BaseClient.prototype.put = function (path, _data, _options) {
        var mock = ValueOf(this.mockdata, cleanPath(path) + ".PUT", false);
        return this.useMockData &&
            mock ?
            mockResponder(mock)
            : _super.prototype.put.apply(this, arguments);
    };
    BaseClient.prototype.post = function (path, _data, _options) {
        var mock = ValueOf(this.mockdata, cleanPath(path) + ".POST", false);
        return this.useMockData &&
            mock ?
            mockResponder(mock)
            : _super.prototype.post.apply(this, arguments);
    };
    BaseClient.prototype.del = function (path, _options) {
        var mock = ValueOf(this.mockdata, cleanPath(path) + ".DELETE", false);
        return this.useMockData &&
            mock ?
            mockResponder(mock)
            : _super.prototype.del.apply(this, arguments);
    };
    BaseClient.prototype.action = function (action) {
        switch (action.method) {
            case "PUT": return this.put(action.href, action.messageBody);
            case "POST": return this.post(action.href, action.messageBody);
            case "DELETE": return this.del(action.href);
            case "GET":
            default:
                return this.get(action.href, action.messageBody);
        }
    };
    Object.defineProperty(BaseClient.prototype, "options", {
        get: function () {
            return {
                url: this.config.api.base,
                cache: false,
                credentials: "include",
                headers: this.config.headers
            };
        },
        enumerable: false,
        configurable: true
    });
    BaseClient = __decorate([
        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,
        __metadata("design:paramtypes", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Object])
    ], BaseClient);
    return BaseClient;
}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseClient));


;// ../src/Context/index.tsx


var WidgetContext = external_root_React_commonjs2_react_commonjs_react_amd_react_.createContext({});
var ContextProvider = WidgetContext.Provider;
var Context = WidgetContext.Consumer;
function withContext(Component) {
    return function (props) {
        return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Context, null, function (context) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Component, __assign({}, props, context)); }));
    };
}

// EXTERNAL MODULE: external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"}
var external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__("redux-observable");
;// ../src/Epics/Lifecycle.ts





var setWidgetStatus = Actions.setWidgetStatus, showHideLoader = Actions.showHideLoader, errorOccured = Actions.errorOccured, clearCachedState = Actions.clearCachedState;
var LifecycleEpics = (function () {
    function LifecycleEpics() {
    }
    LifecycleEpics.prototype.combineEpics = function () {
        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic, this.onErrorOccuredEpic, this.clearCachedStateEpic);
    };
    Object.defineProperty(LifecycleEpics.prototype, "onWidgetStatusEpic", {
        get: function () {
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(setWidgetStatus.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (action) {
                    switch (action.payload) {
                        case EWidgetStatus.INIT: return [showHideLoader(true)];
                        case EWidgetStatus.UPDATING: return [showHideLoader(true)];
                        case EWidgetStatus.RENDERED: return [showHideLoader(false)];
                        case EWidgetStatus.ERROR: return [showHideLoader(false)];
                        default: return [];
                    }
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LifecycleEpics.prototype, "onErrorOccuredEpic", {
        get: function () {
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(errorOccured.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {
                    var payload = _a.payload;
                    return [
                        setWidgetStatus(EWidgetStatus.ERROR)
                    ];
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LifecycleEpics.prototype, "clearCachedStateEpic", {
        get: function () {
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(clearCachedState.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.tap)(function (_a) {
                    var payload = _a.payload;
                    Utils.clearCachedState(payload);
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return []; }));
            };
        },
        enumerable: false,
        configurable: true
    });
    return LifecycleEpics;
}());


;// ../src/Epics/Restrictions.ts





var raiseRestriction = Actions.raiseRestriction, acceptRestriction = Actions.acceptRestriction, declineRestriction = Actions.declineRestriction, finalizeRestriction = Actions.finalizeRestriction, broadcastUpdate = Actions.broadcastUpdate, Restrictions_setWidgetStatus = Actions.setWidgetStatus, historyGo = Actions.historyGo;
var RestricitonsEpics = (function () {
    function RestricitonsEpics(client, restrictionModalId) {
        this.client = client;
        this.restrictionModalId = restrictionModalId;
    }
    RestricitonsEpics.prototype.combineEpics = function () {
        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.raiseRestrictionEpic, this.fadeRestrictionEpic, this.restrictionActionsEpic);
    };
    Object.defineProperty(RestricitonsEpics.prototype, "raiseRestrictionEpic", {
        get: function () {
            var _this = this;
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(raiseRestriction.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {
                    Utils.showLightbox(_this.restrictionModalId || 'RESTRICTIONS_MODAL');
                    return external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.EMPTY;
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RestricitonsEpics.prototype, "fadeRestrictionEpic", {
        get: function () {
            var _this = this;
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(acceptRestriction.toString(), declineRestriction.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {
                    Utils.hideLightbox(_this.restrictionModalId || 'RESTRICTIONS_MODAL');
                    return external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.EMPTY;
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RestricitonsEpics.prototype, "restrictionActionsEpic", {
        get: function () {
            var _this = this;
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Actions.acceptRestriction.toString(), Actions.declineRestriction.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {
                    var payload = _a.payload;
                    return Boolean(payload);
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {
                    var payload = _a.payload;
                    if (payload.redirectURLKey) {
                        return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(broadcastUpdate(historyGo(payload.redirectURLKey)));
                    }
                    if (payload.href) {
                        return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(Restrictions_setWidgetStatus(EWidgetStatus.UPDATING)), _this.client.action(payload).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) {
                            return FilterRestrictionObservable(response, [
                                ValueOf(response, 'data.productOfferingDetail.redirectURLKey', false)
                                    ? historyGo(ValueOf(response, 'data.productOfferingDetail.redirectURLKey'))
                                    : finalizeRestriction(response.data)
                            ]);
                        })));
                    }
                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)();
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(Models.ErrorHandlerObservable(Actions.acceptRestriction)));
            };
        },
        enumerable: false,
        configurable: true
    });
    return RestricitonsEpics;
}());


;// ../src/Epics/Modals.ts




var openLightbox = Actions.openLightbox, closeLightbox = Actions.closeLightbox, setlightboxData = Actions.setlightboxData;
var ModalEpics = (function () {
    function ModalEpics() {
    }
    ModalEpics.prototype.combineEpics = function () {
        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onOpenLightboxEpic, this.closeLightbox);
    };
    Object.defineProperty(ModalEpics.prototype, "onOpenLightboxEpic", {
        get: function () {
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(openLightbox.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {
                    var payload = _a.payload;
                    return !Utils.isLightboxOpen(typeof payload === 'string' ? payload : payload.lightboxId);
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.tap)(function (_a) {
                    var payload = _a.payload;
                    return Utils.showLightbox(typeof payload === 'string' ? payload : payload.lightboxId);
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {
                    var payload = _a.payload;
                    return [
                        setlightboxData(typeof payload === 'string' ? payload : payload.data)
                    ];
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ModalEpics.prototype, "closeLightbox", {
        get: function () {
            return function (action$) {
                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(closeLightbox.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {
                    var payload = _a.payload;
                    return Utils.isLightboxOpen(typeof payload === 'string' ? payload : payload.lightboxId);
                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.tap)(function (_a) {
                    var payload = _a.payload;
                    return Utils.hideLightbox(typeof payload === 'string' ? payload : payload.lightboxId);
                }));
            };
        },
        enumerable: false,
        configurable: true
    });
    return ModalEpics;
}());


;// ../src/Epics/index.ts




;// ../src/Reducers/Lifecycle.ts




var actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;
var _a = actionsToComputedPropertyName(Actions), Lifecycle_setWidgetStatus = _a.setWidgetStatus, Lifecycle_errorOccured = _a.errorOccured;
function getWidgetBaseLifecycle(localization) {
    var _a, _b;
    return {
        localization: localization.createReducer(),
        widgetStatus: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},
            _a[Lifecycle_setWidgetStatus] = function (state, _a) {
                var payload = _a.payload;
                return payload;
            },
            _a), EWidgetStatus.UPDATING),
        error: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},
            _b[Lifecycle_errorOccured] = function (state, _a) {
                var payload = _a.payload;
                return payload;
            },
            _b), null)
    };
}

;// ../src/Reducers/Restrictions.ts



var Restrictions_actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;
var Restrictions_a = Restrictions_actionsToComputedPropertyName(Actions), Restrictions_raiseRestriction = Restrictions_a.raiseRestriction, Restrictions_acceptRestriction = Restrictions_a.acceptRestriction, Restrictions_declineRestriction = Restrictions_a.declineRestriction;
function getWidgetRestrictions() {
    var _a;
    return {
        restriction: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},
            _a[Restrictions_raiseRestriction] = function (state, _a) {
                var payload = _a.payload;
                return payload || state;
            },
            _a[Restrictions_acceptRestriction] = function () { return null; },
            _a[Restrictions_declineRestriction] = function () { return null; },
            _a), null)
    };
}

;// ../src/Reducers/Modals.ts



var Modals_actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;
var Modals_a = Modals_actionsToComputedPropertyName(Actions), Modals_closeLightbox = Modals_a.closeLightbox, Modals_setlightboxData = Modals_a.setlightboxData;
function getWidgetLightboxes() {
    var _a;
    return {
        lightboxData: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},
            _a[Modals_setlightboxData] = function (state, _a) {
                var payload = _a.payload;
                return payload;
            },
            _a[Modals_closeLightbox] = function () { return null; },
            _a), null)
    };
}

;// ../src/Reducers/index.ts



var Reducers;
(function (Reducers) {
    Reducers.WidgetBaseLifecycle = getWidgetBaseLifecycle;
    Reducers.WidgetRestrictions = getWidgetRestrictions;
    Reducers.WidgetLightboxes = getWidgetLightboxes;
})(Reducers || (Reducers = {}));

;// ../src/ViewComponents/Container/Panel.tsx

var PanelComponent = function (_a) {
    var className = _a.className, children = _a.children;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "brf3-panel ".concat(className) }, children);
};
PanelComponent.defaultProps = {
    className: "",
};

;// ../src/ViewComponents/Container/BRF3Container.tsx

var BRF3ContainerComponenet = function (_a) {
    var className = _a.className, children = _a.children;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "brf3-container ".concat(className) }, children);
};
BRF3ContainerComponenet.defaultProps = {
    className: "",
};

;// ../src/ViewComponents/Container/index.tsx

var ContainerComponent = function (_a) {
    var className = _a.className, children = _a.children;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "container liquid-container ".concat(className) }, children);
};
ContainerComponent.defaultProps = {
    className: ""
};



;// ../src/Omniture/Tracker.ts


;// ../src/Omniture/index.tsx



var Omniture;
(function (Omniture) {
    function Ref(elementId) {
        return {
            ref: elementId
        };
    }
    Omniture.Ref = Ref;
    var EMessageType;
    (function (EMessageType) {
        EMessageType["Confirmation"] = "C";
        EMessageType["Information"] = "I";
        EMessageType["Warning"] = "W";
        EMessageType["Error"] = "E";
    })(EMessageType = Omniture.EMessageType || (Omniture.EMessageType = {}));
    var EErrorType;
    (function (EErrorType) {
        EErrorType["Technical"] = "T";
        EErrorType["Business"] = "B";
        EErrorType["Validation"] = "V";
    })(EErrorType = Omniture.EErrorType || (Omniture.EErrorType = {}));
    var EApplicationLayer;
    (function (EApplicationLayer) {
        EApplicationLayer["Browser"] = "BR";
        EApplicationLayer["Frontend"] = "FE";
        EApplicationLayer["ESB"] = "ESB";
        EApplicationLayer["Backend"] = "BE";
        EApplicationLayer["Servicegrid"] = "SG";
        EApplicationLayer["Cache"] = "C";
    })(EApplicationLayer = Omniture.EApplicationLayer || (Omniture.EApplicationLayer = {}));
    Omniture.Component = function (props) {
        var children = props.children, omniture = __rest(props, ["children"]);
        var _childern = external_root_React_commonjs2_react_commonjs_react_amd_react_.Children.toArray(children);
        if (_childern.length > 1)
            throw "Omniture component may not have more then one child";
        return _childern.length > 0 ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null, _childern.map(function (el) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.cloneElement(el, __assign(__assign({}, el.props), { "data-omni": btoa(JSON.stringify(omniture)) })); })) : external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { "data-omni": btoa(JSON.stringify(omniture)) });
    };
    var NOOPE = function () { };
    var _stub = {
        trackPage: NOOPE,
        trackFragment: NOOPE,
        trackAction: NOOPE,
        trackError: NOOPE,
        trackFailure: NOOPE,
        updateContext: NOOPE,
    };
    function useOmniture() {
        return (window["OmnitureTracker"] && window["OmnitureTracker"].getInstance()) || _stub;
    }
    Omniture.useOmniture = useOmniture;
})(Omniture || (Omniture = {}));

;// ../src/ViewComponents/Error/index.tsx






var ErrorComponent = function (_a) {
    var details = _a.details;
    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {
        var action = 0;
        switch (Utils.getFlowType()) {
            case EFlowType.INTERNET:
                action = 543;
                break;
            case EFlowType.TV:
                action = 394;
                break;
        }
        if (details.response ? details.response.url.includes("ProductOrder/Summary") : 0)
            action = 104;
        switch (details.type) {
            case "API":
                Omniture.useOmniture().trackError({
                    code: "API" + ValueOf(details, "response.status", "500"),
                    type: Omniture.EErrorType.Technical,
                    layer: Omniture.EApplicationLayer.Backend,
                    description: {
                        ref: "TechnicalErrorMessage"
                    },
                    ajax: true
                }, action);
                break;
            case "widget":
                Omniture.useOmniture().trackError({
                    code: "WIDGET400",
                    type: Omniture.EErrorType.Technical,
                    layer: Omniture.EApplicationLayer.Frontend,
                    description: {
                        ref: "TechnicalErrorMessage"
                    }
                }, action);
                break;
            case "logic":
            default:
                Omniture.useOmniture().trackError({
                    code: "LOGIC500",
                    type: Omniture.EErrorType.Technical,
                    layer: Omniture.EApplicationLayer.Frontend,
                    description: {
                        ref: "TechnicalErrorMessage"
                    }
                }, action);
                break;
        }
    }, []);
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ContainerComponent, { className: "error margin-30-bottom" },
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "spacer30", "aria-hidden": "true" }),
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PanelComponent, { className: "border bgWgite borderGray4 pad-30" },
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "row" },
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "inlineBlock icon-width-40 valign-middle text-center-xs" },
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "txtRed txtSize32 icons icons-info" })),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "spacer15", "aria-hidden": "true" }),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "inlineBlock pad-20-left no-pad-left-xs content-width valign-middle" },
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "txtBlack2 block txtSize20", id: "TechnicalErrorMessage" },
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: "TECHNICAL_ERROR" })))),
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "margin-20-top", style: { display: details.debug ? "block" : "none" } },
                (function (type) {
                    switch (type) {
                        case "API":
                            return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top" },
                                    "API Request failed ",
                                    ValueOf(details, "response.status", "unknown"),
                                    " (",
                                    ValueOf(details, "response.statusText", "unknown"),
                                    ")"),
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top", style: { wordBreak: "break-all" } },
                                    "URL: ",
                                    ValueOf(details, "response.url", "unknown")),
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top", style: { wordBreak: "break-all" } },
                                    "Response: ",
                                    JSON.stringify(ValueOf(details, "response.data", "Null"), null, " ")));
                        case "widget":
                            return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top" }, "Widget render failed"),
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top" },
                                    "Component: ",
                                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("pre", null, details.componentStack)));
                        case "logic":
                        default:
                            return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top" }, "General logic falure");
                    }
                })(details.type),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "margin-10-top" },
                    "Stack trace: ",
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("pre", null, JSON.stringify(ValueOf(details, "stack"), null, " "))))));
};

// EXTERNAL MODULE: external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"}
var external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__("react-redux");
;// ../src/ViewComponents/VisibilityContainer/index.tsx
var VisibleComponent = function (props) {
    return (typeof props.when === "boolean" ? props.when : Boolean(props.when)) ?
        props.children :
        props.placeholder;
};

;// ../src/ViewComponents/Lightbox/index.tsx






var EModalEvent;
(function (EModalEvent) {
    EModalEvent["SHOW"] = "show.bs.modal";
    EModalEvent["SHOWN"] = "shown.bs.modal";
    EModalEvent["HIDE"] = "hide.bs.modal";
    EModalEvent["HIDDEN"] = "hidden.bs.modal";
})(EModalEvent || (EModalEvent = {}));
var Component = (function (_super) {
    __extends(Component, _super);
    function Component() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Component.prototype.componentDidMount = function () {
        var _this = this;
        this.props.onShow &&
            $("#".concat(this.props.id || this.props.modalId)).on(EModalEvent.SHOW, this.props.onShow);
        this.props.onShown &&
            $("#".concat(this.props.id || this.props.modalId)).on(EModalEvent.SHOWN, this.props.onShown);
        this.props.onHide &&
            $("#".concat(this.props.id || this.props.modalId)).on(EModalEvent.HIDE, this.props.onHide);
        this.props.onHidden &&
            $("#".concat(this.props.id || this.props.modalId)).on(EModalEvent.HIDDEN, this.props.onHidden);
        this.onClose = this.onClose.bind(this);
        $("#".concat(this.props.id || this.props.modalId)).on(EModalEvent.HIDDEN, function () {
            var domElem = _this.props.lightboxData && _this.props.lightboxData.relativeId && document.getElementById(_this.props.lightboxData.relativeId);
            domElem && domElem.focus();
            _this.props.clearLightboxData();
        });
    };
    Component.prototype.onClose = function () {
        if (Utils.isLightboxOpen(this.props.id || this.props.modalId)) {
            this.props.onCloseLightbox(this.props.id || this.props.modalId);
            this.props.onClose !== undefined && this.props.onClose(this.props.id || this.props.modalId);
        }
    };
    Component.prototype.render = function () {
        var _a = this.props, id = _a.id, _b = _a.className, className = _b === void 0 ? "" : _b, size = _a.size, title = _a.title, _c = _a.containerClass, containerClass = _c === void 0 ? [] : _c, modalId = _a.modalId, children = _a.children, onDismiss = _a.onDismiss, permanent = _a.permanent;
        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { id: id || modalId, className: "modal modal-vm fade ".concat(className), role: "dialog", tabIndex: -1, "data-backdrop": "static", "data-keyboard": "false", "aria-modal": "true", "aria-labelledby": "".concat(id || modalId, "_label"), "aria-hidden": "true" },
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "sr-only" }, "dialog"),
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "modal-dialog modal-md modal-bg modal-".concat(size, " bell-modal-").concat(size), role: "document" },
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "modal-content noBorderRadius noBorder-xs" },
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "modal-header bgGrayLight2 pad-30-left pad-30-right pad-25-top pad-25-bottom pad-15-left-right-xs align-items-center noBorderRadius accss-focus-outline-override-grey-bg" },
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("h2", { id: "".concat(id || modalId, "_label"), className: "virginUltra txtBlack txtSize24 overflow-ellipsis txtSize18-xs txtUppercase sans-serif-xs lineHeight1_5 margin-b-0" }, title),
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: !permanent },
                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("button", { onClick: onDismiss, id: "close_".concat(id || modalId), type: "button", className: "no-pad close", "data-dismiss": "modal", "aria-label": "Close Dialog", "aria-describedby": "".concat(id || modalId, "_label"), autoFocus: true },
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "volt-icon icon-big_X" })))),
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { id: "".concat(id || modalId, "_desc"), className: "modal-body pad-0 ".concat(containerClass.join(" ")) }, children))));
    };
    Component.prototype.componentWillUnmount = function () {
        this.props.onShow &&
            $("#".concat(this.props.id || this.props.modalId)).off(EModalEvent.SHOW, this.props.onShow);
        this.props.onShown &&
            $("#".concat(this.props.id || this.props.modalId)).off(EModalEvent.SHOWN, this.props.onShown);
        this.props.onHide &&
            $("#".concat(this.props.id || this.props.modalId)).off(EModalEvent.HIDE, this.props.onHide);
        this.props.onHidden &&
            $("#".concat(this.props.id || this.props.modalId)).off(EModalEvent.HIDDEN, this.props.onHidden);
    };
    Component.defaultProps = {
        className: "",
        size: "md"
    };
    return Component;
}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));

var LightboxContainer = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {
    var lightboxData = _a.lightboxData;
    return ({ lightboxData: lightboxData });
}, function (dispatch) { return ({
    onCloseLightbox: function (modalId) { return dispatch(Actions.closeLightbox(modalId)); },
    clearLightboxData: function () { return dispatch(Actions.setlightboxData("")); }
}); })(Component);

;// ../src/ViewComponents/Restriction/index.tsx









function getButtonClass(i) {
    switch (true) {
        case i === 0: return "btn btn-primary fill-xs";
        case i === 1: return "btn btn-default fill-xs";
        default: return "btn btn-link";
    }
}
function getMessageIcon(type) {
    switch (type) {
        case "Error": return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "icon2 icon-alert-circled txtSize38 txtRed pad-15-right" });
        case "Information": return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "icon2 icon-alert-circled txtSize38 txtYellow pad-15-right" });
        case "Warning": return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "icon2 icon-alert-circled txtSize38 txtYellow pad-15-right" });
        default: return null;
    }
}
var lightboxType = "";
function onShowOmniture(id) {
    var messageType;
    var s_oAPT = {
        actionId: 104,
        actionresult: 0,
        applicationState: 0
    };
    switch (lightboxType) {
        case "Warning":
        case "Error":
            s_oAPT.actionresult = 2;
            s_oAPT.applicationState = 2;
            messageType = Omniture.EMessageType.Warning;
            break;
        case "Information":
        default:
            messageType = Omniture.EMessageType.Information;
    }
    Omniture.useOmniture().trackFragment({
        id: "restrictionLightbox",
        s_oAPT: s_oAPT,
        s_oPRM: {
            ref: "".concat(id, "_label")
        },
        s_oLBC: {
            ref: "".concat(id, "_description")
        },
        s_oPLE: {
            content: {
                ref: "".concat(id, "_description")
            },
            type: messageType
        }
    });
}
;
var Restriction_Component = function (_a) {
    var id = _a.id, type = _a.type, title = _a.title, description = _a.description, dynamicData = _a.dynamicData, footerDescription = _a.footerDescription, actionLinks = _a.actionLinks, onDismiss = _a.onDismiss, onAction = _a.onAction, onComplete = _a.onComplete;
    lightboxType = type;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(LightboxContainer, { modalId: id || "RESTRICTIONS_MODAL", permanent: true, onClose: function () { onDismiss(); onComplete && onComplete("close"); }, onShown: function () { return onShowOmniture(id); }, title: title },
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "modal-body bgWhite" },
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "flex" },
                getMessageIcon(type || ""),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { id: "".concat(id, "_description") },
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { dangerouslySetInnerHTML: { __html: description } }),
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: ValueOf(dynamicData, "productList.length", false) },
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("ul", null, ValueOf(dynamicData, "productList", []).map(function (product) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("li", { className: "txtBold txtBlack" }, product); }))),
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: ValueOf(dynamicData, "promotion.length", false) },
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("ul", null, ValueOf(dynamicData, "promotion", []).map(function (promo) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("li", null,
                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "txtBold txtBlack" }, promo.promoName),
                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("br", null),
                            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: Boolean(promo.promoExpiry) },
                                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: promo.promoExpiry, format: "yMMMd", timeZone: "UTC" }, function (expiryDate) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: "PromotionExpires", values: { expiryDate: expiryDate } }); }))); }))),
                    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: Boolean(footerDescription) },
                        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "txtBold", dangerouslySetInnerHTML: { __html: footerDescription } }))))),
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(VisibleComponent, { when: Boolean(actionLinks && actionLinks.length > 0) },
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "spacer1 bgGrayLight6", "aria-hidden": "true" }),
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "bgGray19 pad-30 pad-15-left-right-xs" }, ValueOf(actionLinks, undefined, [])
                .map(function (action, i) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("button", { id: "ACTION_SUBMIT", className: getButtonClass(i), onClick: function () { onAction(action); onComplete && onComplete(action.rel); } }, action.name),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("div", { className: "vSpacer15", "aria-hidden": "true" })); }))));
};
var RestrictionModalView = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {
    var restriction = _a.restriction;
    return (restriction ? __assign({}, restriction) : {});
}, function (dispatch) { return ({
    onAction: function (action) {
        Omniture.useOmniture().trackAction({
            id: "restrictionLightbox",
            s_oAPT: {
                actionId: 647,
                actionresult: 0,
                applicationState: 0
            },
            s_oBTN: action.name
        });
        switch (action.name) {
            case "Cancel":
                dispatch(Actions.declineRestriction(action));
                break;
            default: dispatch(Actions.acceptRestriction(action));
        }
    },
    onDismiss: function () { return dispatch(Actions.declineRestriction()); }
}); })(Restriction_Component);

;// ../src/ViewComponents/EllipsisText/index.tsx

var EllipsisText = function (props) {
    var text = props.text, maxLength = props.maxLength, className = props.className;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("p", { className: "ellipsis-text ".concat(className) }, text.length <= maxLength ? text : "".concat(text.substring(0, maxLength), "..."));
};
EllipsisText.defaultProps = {
    className: ""
};
EllipsisText.displayName = "EllipsisText";

;// ../src/ViewComponents/Application/index.tsx






var Application_Component = function (props) { return (Boolean(props.localization &&
    props.localization.messages &&
    Object.keys(props.localization.messages).length) ?
    external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.IntlProvider, __assign({}, props.localization, { formats: __assign(__assign({}, props.localization.formats), { number: { CAD: { currency: "CAD", currencyDisplay: "symbol", style: "currency", minimumFractionDigits: 2 } } }), locale: props.localization.fullLocale }),
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null, (function (status) {
            switch (status) {
                case EWidgetStatus.RENDERED:
                case EWidgetStatus.UPDATING:
                    return props.children;
                case EWidgetStatus.ERROR:
                    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(ErrorComponent, { details: props.errorHandlerProps });
                default:
                    return props.placeholder;
            }
        })(props.widgetStatus))) : null); };
var ApplicationRootComponent = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {
    var localization = _a.localization, error = _a.error, widgetStatus = _a.widgetStatus;
    return ({
        localization: localization,
        widgetStatus: widgetStatus,
        errorHandlerProps: error
    });
}, {}, function (stateProps, despatchProps, ownProps) {
    return __assign(__assign(__assign({}, (ownProps.propsfilter ? ownProps.propsfilter(stateProps) : stateProps)), despatchProps), ownProps);
})(Application_Component);

;// ../src/ViewComponents/Localization/BellCurrency/BellCurrency.tsx


function padBellCurrency(cents) {
    if (!cents)
        return "00";
    if (cents.length === 1)
        return "".concat(cents, "0");
    return cents.slice(0, 2);
}
function getBellCurrency(localization, value) {
    try {
        var n = String(value).split(".");
        var dollars = n[0];
        var cents = n[1];
        var credit = value < 0 ? true : false;
        dollars = dollars.replace("-", "");
        if (Number(dollars) > 0 && (!cents || Number(cents) === 0)) {
        }
        switch (localization.locale) {
            default:
            case "en":
                var enDollars = parseInt(dollars).toLocaleString("en");
                if (credit) {
                    return "CR $".concat(enDollars, ".").concat(padBellCurrency(cents));
                }
                else {
                    return "$".concat(enDollars, ".").concat(padBellCurrency(cents));
                }
            case "fr":
                var frDollars = parseInt(dollars).toLocaleString("fr");
                if (credit) {
                    return "CR ".concat(frDollars, ",").concat(padBellCurrency(cents), "&nbsp;$");
                }
                else {
                    return "".concat(frDollars, ",").concat(padBellCurrency(cents), "&nbsp;$");
                }
        }
    }
    catch (e) {
        return value;
    }
}
var Componenet = function (_a) {
    var value = _a.value, className = _a.className, localization = _a.localization, tag = _a.tag, tagProps = _a.tagProps, credit = _a.credit;
    var CustomTag = tag || "span";
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(CustomTag, __assign({}, tagProps, { className: "txtCurrency ".concat(className || ""), dangerouslySetInnerHTML: { __html: getBellCurrency(localization, value) } }));
};
/* harmony default export */ const BellCurrency = (Componenet);

;// ../src/ViewComponents/Localization/BellCurrency/index.ts


var BellCurrencyComponent = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {
    var localization = _a.localization;
    return ({ localization: localization });
}, function (dispatch) { return ({}); })(BellCurrency);
BellCurrencyComponent.displayName = "BellCurrency";

;// ../src/ViewComponents/Localization/FormattedMessage/index.tsx




var BrandedMessageComponent = function (_a) {
    var id = _a.id, props = __rest(_a, ["id"]);
    return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Context, null, function (_a) {
        var config = _a.config;
        return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, __assign({}, props, { id: "".concat(config.environmentVariables.brand, "_").concat(id) }));
    }));
};

;// ../src/ViewComponents/Localization/Currency/index.tsx


var Currency = function (_a) {
    var str = _a.str, prefixClassName = _a.prefixClassName, fractionClassName = _a.fractionClassName;
    var whole = "", fraction = "", prefix = "";
    if (str.indexOf("$") === 0) {
        var parts = str.split(".");
        prefix = parts[0].substr(0, 1);
        whole = parts[0].substr(1);
        fraction = parts[1];
    }
    else {
        var parts = str.split(",");
        whole = parts[0];
        fraction = parts[1];
    }
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_React_commonjs2_react_commonjs_react_amd_react_.Fragment, null,
        Boolean(prefix) ? external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("sup", { className: prefixClassName }, prefix) : null,
        whole,
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("sup", { className: fractionClassName, "aria-hidden": true }, fraction),
        fraction !== "00" && external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "sr-only" },
            ".",
            fraction,
            " cents"));
};
var CurrencyComponent = function (_a) {
    var className = _a.className, prefixClassName = _a.prefixClassName, fractionClassName = _a.fractionClassName, value = _a.value, monthly = _a.monthly;
    return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedNumber, { value: value, format: "CAD" }, function (str) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "formatted-currency ".concat(className) },
        external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(Currency, { str: str, prefixClassName: prefixClassName, fractionClassName: fractionClassName }),
        monthly ?
            external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("sup", { className: prefixClassName },
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: "PER_MO" }, function (per_mo) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { "aria-hidden": "true" }, per_mo); }),
                external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: "PER_MONTH" }, function (per_month) { return external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement("span", { className: "sr-only" }, per_month); })) : null); });
};
CurrencyComponent.defaultProps = {
    className: "",
    prefixClassName: "txtSize22",
    fractionClassName: "txtSize22"
};

;// ../src/ViewComponents/Localization/index.ts




;// ./redux-persist/es/integration/react.js
function react_typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { react_typeof = function _typeof(obj) { return typeof obj; }; } else { react_typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return react_typeof(obj); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

function _possibleConstructorReturn(self, call) { if (call && (react_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

function react_defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

 // eslint-disable-line import/no-unresolved

var PersistGate =
/*#__PURE__*/
function (_PureComponent) {
  _inherits(PersistGate, _PureComponent);

  function PersistGate() {
    var _getPrototypeOf2;

    var _this;

    _classCallCheck(this, PersistGate);

    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(PersistGate)).call.apply(_getPrototypeOf2, [this].concat(args)));

    react_defineProperty(_assertThisInitialized(_this), "state", {
      bootstrapped: false
    });

    react_defineProperty(_assertThisInitialized(_this), "_unsubscribe", void 0);

    react_defineProperty(_assertThisInitialized(_this), "handlePersistorState", function () {
      var persistor = _this.props.persistor;

      var _persistor$getState = persistor.getState(),
          bootstrapped = _persistor$getState.bootstrapped;

      if (bootstrapped) {
        if (_this.props.onBeforeLift) {
          Promise.resolve(_this.props.onBeforeLift()).finally(function () {
            return _this.setState({
              bootstrapped: true
            });
          });
        } else {
          _this.setState({
            bootstrapped: true
          });
        }

        _this._unsubscribe && _this._unsubscribe();
      }
    });

    return _this;
  }

  _createClass(PersistGate, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this._unsubscribe = this.props.persistor.subscribe(this.handlePersistorState);
      this.handlePersistorState();
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this._unsubscribe && this._unsubscribe();
    }
  }, {
    key: "render",
    value: function render() {
      if (true) {
        if (typeof this.props.children === 'function' && this.props.loading) console.error('redux-persist: PersistGate expects either a function child or loading prop, but not both. The loading prop will be ignored.');
      }

      if (typeof this.props.children === 'function') {
        return this.props.children(this.state.bootstrapped);
      }

      return this.state.bootstrapped ? this.props.children : this.props.loading;
    }
  }]);

  return PersistGate;
}(external_root_React_commonjs2_react_commonjs_react_amd_react_.PureComponent);

react_defineProperty(PersistGate, "defaultProps", {
  children: null,
  loading: null
});
;// ../src/ViewComponents/ReduxPersistGate/index.tsx



var ReduxPersistGate = function (props) { return (external_root_React_commonjs2_react_commonjs_react_amd_react_.createElement(PersistGate, { loading: null, persistor: persistStore(props.store) }, props.render())); };

;// ../src/ViewComponents/index.ts









var Components;
(function (Components) {
    Components.Error = ErrorComponent;
    Components.Container = ContainerComponent;
    Components.Panel = PanelComponent;
    Components.BRF3Container = BRF3ContainerComponenet;
    Components.Modal = LightboxContainer;
    Components.RestrictionModal = RestrictionModalView;
    Components.ApplicationRoot = ApplicationRootComponent;
    Components.EllipsisText = EllipsisText;
    Components.Currency = CurrencyComponent;
    Components.BellCurrency = BellCurrencyComponent;
    Components.BrandedMessage = BrandedMessageComponent;
    Components.PersistGate = ReduxPersistGate;
    Components.Visible = VisibleComponent;
})(Components || (Components = {}));

;// ../src/index.ts










})();

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=omf-changepackage-components.js.map